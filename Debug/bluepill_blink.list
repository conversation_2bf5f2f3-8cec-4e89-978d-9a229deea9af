
bluepill_blink.elf:     file format elf32-littlearm

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .isr_vector   0000010c  08000000  08000000  00001000  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  1 .text         00001058  0800010c  0800010c  0000110c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  2 .rodata       00000024  08001164  08001164  00002164  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  3 .ARM.extab    00000000  08001188  08001188  0000300c  2**0
                  CONTENTS, READONLY
  4 .ARM          00000000  08001188  08001188  0000300c  2**0
                  CONTENTS, READONLY
  5 .preinit_array 00000000  08001188  08001188  0000300c  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  6 .init_array   00000004  08001188  08001188  00002188  2**2
                  CONTENTS, ALLOC, LOAD, R<PERSON><PERSON><PERSON><PERSON><PERSON>, DATA
  7 .fini_array   00000004  0800118c  0800118c  0000218c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  8 .data         0000000c  20000000  08001190  00003000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  9 .bss          00000020  2000000c  0800119c  0000300c  2**2
                  ALLOC
 10 ._user_heap_stack 00000604  2000002c  0800119c  0000302c  2**0
                  ALLOC
 11 .ARM.attributes 00000029  00000000  00000000  0000300c  2**0
                  CONTENTS, READONLY
 12 .debug_info   0000284d  00000000  00000000  00003035  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 13 .debug_abbrev 00000de9  00000000  00000000  00005882  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 14 .debug_aranges 000003d0  00000000  00000000  00006670  2**3
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 15 .debug_rnglists 000002b8  00000000  00000000  00006a40  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 16 .debug_macro  00015958  00000000  00000000  00006cf8  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 17 .debug_line   00004622  00000000  00000000  0001c650  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 18 .debug_str    0007bcac  00000000  00000000  00020c72  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 19 .comment      00000043  00000000  00000000  0009c91e  2**0
                  CONTENTS, READONLY
 20 .debug_frame  00000d2c  00000000  00000000  0009c964  2**2
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 21 .debug_line_str 00000068  00000000  00000000  0009d690  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS

Disassembly of section .text:

0800010c <__do_global_dtors_aux>:
 800010c:	b510      	push	{r4, lr}
 800010e:	4c05      	ldr	r4, [pc, #20]	@ (8000124 <__do_global_dtors_aux+0x18>)
 8000110:	7823      	ldrb	r3, [r4, #0]
 8000112:	b933      	cbnz	r3, 8000122 <__do_global_dtors_aux+0x16>
 8000114:	4b04      	ldr	r3, [pc, #16]	@ (8000128 <__do_global_dtors_aux+0x1c>)
 8000116:	b113      	cbz	r3, 800011e <__do_global_dtors_aux+0x12>
 8000118:	4804      	ldr	r0, [pc, #16]	@ (800012c <__do_global_dtors_aux+0x20>)
 800011a:	f3af 8000 	nop.w
 800011e:	2301      	movs	r3, #1
 8000120:	7023      	strb	r3, [r4, #0]
 8000122:	bd10      	pop	{r4, pc}
 8000124:	2000000c 	.word	0x2000000c
 8000128:	00000000 	.word	0x00000000
 800012c:	0800114c 	.word	0x0800114c

08000130 <frame_dummy>:
 8000130:	b508      	push	{r3, lr}
 8000132:	4b03      	ldr	r3, [pc, #12]	@ (8000140 <frame_dummy+0x10>)
 8000134:	b11b      	cbz	r3, 800013e <frame_dummy+0xe>
 8000136:	4903      	ldr	r1, [pc, #12]	@ (8000144 <frame_dummy+0x14>)
 8000138:	4803      	ldr	r0, [pc, #12]	@ (8000148 <frame_dummy+0x18>)
 800013a:	f3af 8000 	nop.w
 800013e:	bd08      	pop	{r3, pc}
 8000140:	00000000 	.word	0x00000000
 8000144:	20000010 	.word	0x20000010
 8000148:	0800114c 	.word	0x0800114c

0800014c <main>:
/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
 800014c:	b580      	push	{r7, lr}
 800014e:	af00      	add	r7, sp, #0
  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();
 8000150:	f000 f910 	bl	8000374 <HAL_Init>
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();
 8000154:	f000 f818 	bl	8000188 <SystemClock_Config>
  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
 8000158:	f000 f858 	bl	800020c <MX_GPIO_Init>
  
  /* USER CODE BEGIN 2 */
  
  /* Ensure GPIO is properly initialized and wait for system to stabilize */
  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
 800015c:	2200      	movs	r2, #0
 800015e:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 8000162:	4808      	ldr	r0, [pc, #32]	@ (8000184 <main+0x38>)
 8000164:	f000 fbd0 	bl	8000908 <HAL_GPIO_WritePin>

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
	  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);
 8000168:	2201      	movs	r2, #1
 800016a:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 800016e:	4805      	ldr	r0, [pc, #20]	@ (8000184 <main+0x38>)
 8000170:	f000 fbca 	bl	8000908 <HAL_GPIO_WritePin>

	  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
 8000174:	2200      	movs	r2, #0
 8000176:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 800017a:	4802      	ldr	r0, [pc, #8]	@ (8000184 <main+0x38>)
 800017c:	f000 fbc4 	bl	8000908 <HAL_GPIO_WritePin>
	  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);
 8000180:	bf00      	nop
 8000182:	e7f1      	b.n	8000168 <main+0x1c>
 8000184:	40011000 	.word	0x40011000

08000188 <SystemClock_Config>:
/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
 8000188:	b580      	push	{r7, lr}
 800018a:	b090      	sub	sp, #64	@ 0x40
 800018c:	af00      	add	r7, sp, #0
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
 800018e:	f107 0318 	add.w	r3, r7, #24
 8000192:	2228      	movs	r2, #40	@ 0x28
 8000194:	2100      	movs	r1, #0
 8000196:	4618      	mov	r0, r3
 8000198:	f000 ffac 	bl	80010f4 <memset>
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 800019c:	1d3b      	adds	r3, r7, #4
 800019e:	2200      	movs	r2, #0
 80001a0:	601a      	str	r2, [r3, #0]
 80001a2:	605a      	str	r2, [r3, #4]
 80001a4:	609a      	str	r2, [r3, #8]
 80001a6:	60da      	str	r2, [r3, #12]
 80001a8:	611a      	str	r2, [r3, #16]

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
 80001aa:	2302      	movs	r3, #2
 80001ac:	61bb      	str	r3, [r7, #24]
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
 80001ae:	2301      	movs	r3, #1
 80001b0:	62bb      	str	r3, [r7, #40]	@ 0x28
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
 80001b2:	2310      	movs	r3, #16
 80001b4:	62fb      	str	r3, [r7, #44]	@ 0x2c
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
 80001b6:	2302      	movs	r3, #2
 80001b8:	637b      	str	r3, [r7, #52]	@ 0x34
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI_DIV2;
 80001ba:	2300      	movs	r3, #0
 80001bc:	63bb      	str	r3, [r7, #56]	@ 0x38
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL16;
 80001be:	f44f 1360 	mov.w	r3, #3670016	@ 0x380000
 80001c2:	63fb      	str	r3, [r7, #60]	@ 0x3c
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 80001c4:	f107 0318 	add.w	r3, r7, #24
 80001c8:	4618      	mov	r0, r3
 80001ca:	f000 fbb5 	bl	8000938 <HAL_RCC_OscConfig>
 80001ce:	4603      	mov	r3, r0
 80001d0:	2b00      	cmp	r3, #0
 80001d2:	d001      	beq.n	80001d8 <SystemClock_Config+0x50>
  {
    Error_Handler();
 80001d4:	f000 f84e 	bl	8000274 <Error_Handler>
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
 80001d8:	230f      	movs	r3, #15
 80001da:	607b      	str	r3, [r7, #4]
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
 80001dc:	2302      	movs	r3, #2
 80001de:	60bb      	str	r3, [r7, #8]
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
 80001e0:	2300      	movs	r3, #0
 80001e2:	60fb      	str	r3, [r7, #12]
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
 80001e4:	f44f 6380 	mov.w	r3, #1024	@ 0x400
 80001e8:	613b      	str	r3, [r7, #16]
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
 80001ea:	2300      	movs	r3, #0
 80001ec:	617b      	str	r3, [r7, #20]

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
 80001ee:	1d3b      	adds	r3, r7, #4
 80001f0:	2102      	movs	r1, #2
 80001f2:	4618      	mov	r0, r3
 80001f4:	f000 fe22 	bl	8000e3c <HAL_RCC_ClockConfig>
 80001f8:	4603      	mov	r3, r0
 80001fa:	2b00      	cmp	r3, #0
 80001fc:	d001      	beq.n	8000202 <SystemClock_Config+0x7a>
  {
    Error_Handler();
 80001fe:	f000 f839 	bl	8000274 <Error_Handler>
  }
}
 8000202:	bf00      	nop
 8000204:	3740      	adds	r7, #64	@ 0x40
 8000206:	46bd      	mov	sp, r7
 8000208:	bd80      	pop	{r7, pc}
	...

0800020c <MX_GPIO_Init>:
  * @brief GPIO Initialization Function
  * @param None
  * @retval None
  */
static void MX_GPIO_Init(void)
{
 800020c:	b580      	push	{r7, lr}
 800020e:	b086      	sub	sp, #24
 8000210:	af00      	add	r7, sp, #0
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 8000212:	f107 0308 	add.w	r3, r7, #8
 8000216:	2200      	movs	r2, #0
 8000218:	601a      	str	r2, [r3, #0]
 800021a:	605a      	str	r2, [r3, #4]
 800021c:	609a      	str	r2, [r3, #8]
 800021e:	60da      	str	r2, [r3, #12]
  /* USER CODE BEGIN MX_GPIO_Init_1 */

  /* USER CODE END MX_GPIO_Init_1 */

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOC_CLK_ENABLE();
 8000220:	4b12      	ldr	r3, [pc, #72]	@ (800026c <MX_GPIO_Init+0x60>)
 8000222:	699b      	ldr	r3, [r3, #24]
 8000224:	4a11      	ldr	r2, [pc, #68]	@ (800026c <MX_GPIO_Init+0x60>)
 8000226:	f043 0310 	orr.w	r3, r3, #16
 800022a:	6193      	str	r3, [r2, #24]
 800022c:	4b0f      	ldr	r3, [pc, #60]	@ (800026c <MX_GPIO_Init+0x60>)
 800022e:	699b      	ldr	r3, [r3, #24]
 8000230:	f003 0310 	and.w	r3, r3, #16
 8000234:	607b      	str	r3, [r7, #4]
 8000236:	687b      	ldr	r3, [r7, #4]

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);
 8000238:	2200      	movs	r2, #0
 800023a:	f44f 5100 	mov.w	r1, #8192	@ 0x2000
 800023e:	480c      	ldr	r0, [pc, #48]	@ (8000270 <MX_GPIO_Init+0x64>)
 8000240:	f000 fb62 	bl	8000908 <HAL_GPIO_WritePin>

  /*Configure GPIO pin : PC13 */
  GPIO_InitStruct.Pin = GPIO_PIN_13;
 8000244:	f44f 5300 	mov.w	r3, #8192	@ 0x2000
 8000248:	60bb      	str	r3, [r7, #8]
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 800024a:	2301      	movs	r3, #1
 800024c:	60fb      	str	r3, [r7, #12]
  GPIO_InitStruct.Pull = GPIO_NOPULL;
 800024e:	2300      	movs	r3, #0
 8000250:	613b      	str	r3, [r7, #16]
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 8000252:	2302      	movs	r3, #2
 8000254:	617b      	str	r3, [r7, #20]
  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
 8000256:	f107 0308 	add.w	r3, r7, #8
 800025a:	4619      	mov	r1, r3
 800025c:	4804      	ldr	r0, [pc, #16]	@ (8000270 <MX_GPIO_Init+0x64>)
 800025e:	f000 f9cf 	bl	8000600 <HAL_GPIO_Init>

  /* USER CODE BEGIN MX_GPIO_Init_2 */

  /* USER CODE END MX_GPIO_Init_2 */
}
 8000262:	bf00      	nop
 8000264:	3718      	adds	r7, #24
 8000266:	46bd      	mov	sp, r7
 8000268:	bd80      	pop	{r7, pc}
 800026a:	bf00      	nop
 800026c:	40021000 	.word	0x40021000
 8000270:	40011000 	.word	0x40011000

08000274 <Error_Handler>:
/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
 8000274:	b480      	push	{r7}
 8000276:	af00      	add	r7, sp, #0
  \details Disables IRQ interrupts by setting the I-bit in the CPSR.
           Can only be executed in Privileged modes.
 */
__STATIC_FORCEINLINE void __disable_irq(void)
{
  __ASM volatile ("cpsid i" : : : "memory");
 8000278:	b672      	cpsid	i
}
 800027a:	bf00      	nop
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
 800027c:	bf00      	nop
 800027e:	e7fd      	b.n	800027c <Error_Handler+0x8>

08000280 <HAL_MspInit>:
/* USER CODE END 0 */
/**
  * Initializes the Global MSP.
  */
void HAL_MspInit(void)
{
 8000280:	b480      	push	{r7}
 8000282:	b083      	sub	sp, #12
 8000284:	af00      	add	r7, sp, #0

  /* USER CODE BEGIN MspInit 0 */

  /* USER CODE END MspInit 0 */

  __HAL_RCC_AFIO_CLK_ENABLE();
 8000286:	4b0e      	ldr	r3, [pc, #56]	@ (80002c0 <HAL_MspInit+0x40>)
 8000288:	699b      	ldr	r3, [r3, #24]
 800028a:	4a0d      	ldr	r2, [pc, #52]	@ (80002c0 <HAL_MspInit+0x40>)
 800028c:	f043 0301 	orr.w	r3, r3, #1
 8000290:	6193      	str	r3, [r2, #24]
 8000292:	4b0b      	ldr	r3, [pc, #44]	@ (80002c0 <HAL_MspInit+0x40>)
 8000294:	699b      	ldr	r3, [r3, #24]
 8000296:	f003 0301 	and.w	r3, r3, #1
 800029a:	607b      	str	r3, [r7, #4]
 800029c:	687b      	ldr	r3, [r7, #4]
  __HAL_RCC_PWR_CLK_ENABLE();
 800029e:	4b08      	ldr	r3, [pc, #32]	@ (80002c0 <HAL_MspInit+0x40>)
 80002a0:	69db      	ldr	r3, [r3, #28]
 80002a2:	4a07      	ldr	r2, [pc, #28]	@ (80002c0 <HAL_MspInit+0x40>)
 80002a4:	f043 5380 	orr.w	r3, r3, #268435456	@ 0x10000000
 80002a8:	61d3      	str	r3, [r2, #28]
 80002aa:	4b05      	ldr	r3, [pc, #20]	@ (80002c0 <HAL_MspInit+0x40>)
 80002ac:	69db      	ldr	r3, [r3, #28]
 80002ae:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 80002b2:	603b      	str	r3, [r7, #0]
 80002b4:	683b      	ldr	r3, [r7, #0]
  /* System interrupt init*/

  /* USER CODE BEGIN MspInit 1 */

  /* USER CODE END MspInit 1 */
}
 80002b6:	bf00      	nop
 80002b8:	370c      	adds	r7, #12
 80002ba:	46bd      	mov	sp, r7
 80002bc:	bc80      	pop	{r7}
 80002be:	4770      	bx	lr
 80002c0:	40021000 	.word	0x40021000

080002c4 <NMI_Handler>:
/******************************************************************************/
/**
  * @brief This function handles Non maskable interrupt.
  */
void NMI_Handler(void)
{
 80002c4:	b480      	push	{r7}
 80002c6:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN NonMaskableInt_IRQn 0 */

  /* USER CODE END NonMaskableInt_IRQn 0 */
  /* USER CODE BEGIN NonMaskableInt_IRQn 1 */
   while (1)
 80002c8:	bf00      	nop
 80002ca:	e7fd      	b.n	80002c8 <NMI_Handler+0x4>

080002cc <HardFault_Handler>:

/**
  * @brief This function handles Hard fault interrupt.
  */
void HardFault_Handler(void)
{
 80002cc:	b480      	push	{r7}
 80002ce:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN HardFault_IRQn 0 */

  /* USER CODE END HardFault_IRQn 0 */
  while (1)
 80002d0:	bf00      	nop
 80002d2:	e7fd      	b.n	80002d0 <HardFault_Handler+0x4>

080002d4 <MemManage_Handler>:

/**
  * @brief This function handles Memory management fault.
  */
void MemManage_Handler(void)
{
 80002d4:	b480      	push	{r7}
 80002d6:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN MemoryManagement_IRQn 0 */

  /* USER CODE END MemoryManagement_IRQn 0 */
  while (1)
 80002d8:	bf00      	nop
 80002da:	e7fd      	b.n	80002d8 <MemManage_Handler+0x4>

080002dc <BusFault_Handler>:

/**
  * @brief This function handles Prefetch fault, memory access fault.
  */
void BusFault_Handler(void)
{
 80002dc:	b480      	push	{r7}
 80002de:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN BusFault_IRQn 0 */

  /* USER CODE END BusFault_IRQn 0 */
  while (1)
 80002e0:	bf00      	nop
 80002e2:	e7fd      	b.n	80002e0 <BusFault_Handler+0x4>

080002e4 <UsageFault_Handler>:

/**
  * @brief This function handles Undefined instruction or illegal state.
  */
void UsageFault_Handler(void)
{
 80002e4:	b480      	push	{r7}
 80002e6:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN UsageFault_IRQn 0 */

  /* USER CODE END UsageFault_IRQn 0 */
  while (1)
 80002e8:	bf00      	nop
 80002ea:	e7fd      	b.n	80002e8 <UsageFault_Handler+0x4>

080002ec <SVC_Handler>:

/**
  * @brief This function handles System service call via SWI instruction.
  */
void SVC_Handler(void)
{
 80002ec:	b480      	push	{r7}
 80002ee:	af00      	add	r7, sp, #0

  /* USER CODE END SVCall_IRQn 0 */
  /* USER CODE BEGIN SVCall_IRQn 1 */

  /* USER CODE END SVCall_IRQn 1 */
}
 80002f0:	bf00      	nop
 80002f2:	46bd      	mov	sp, r7
 80002f4:	bc80      	pop	{r7}
 80002f6:	4770      	bx	lr

080002f8 <DebugMon_Handler>:

/**
  * @brief This function handles Debug monitor.
  */
void DebugMon_Handler(void)
{
 80002f8:	b480      	push	{r7}
 80002fa:	af00      	add	r7, sp, #0

  /* USER CODE END DebugMonitor_IRQn 0 */
  /* USER CODE BEGIN DebugMonitor_IRQn 1 */

  /* USER CODE END DebugMonitor_IRQn 1 */
}
 80002fc:	bf00      	nop
 80002fe:	46bd      	mov	sp, r7
 8000300:	bc80      	pop	{r7}
 8000302:	4770      	bx	lr

08000304 <PendSV_Handler>:

/**
  * @brief This function handles Pendable request for system service.
  */
void PendSV_Handler(void)
{
 8000304:	b480      	push	{r7}
 8000306:	af00      	add	r7, sp, #0

  /* USER CODE END PendSV_IRQn 0 */
  /* USER CODE BEGIN PendSV_IRQn 1 */

  /* USER CODE END PendSV_IRQn 1 */
}
 8000308:	bf00      	nop
 800030a:	46bd      	mov	sp, r7
 800030c:	bc80      	pop	{r7}
 800030e:	4770      	bx	lr

08000310 <SysTick_Handler>:

/**
  * @brief This function handles System tick timer.
  */
void SysTick_Handler(void)
{
 8000310:	b580      	push	{r7, lr}
 8000312:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN SysTick_IRQn 0 */

  /* USER CODE END SysTick_IRQn 0 */
  HAL_IncTick();
 8000314:	f000 f874 	bl	8000400 <HAL_IncTick>
  /* USER CODE BEGIN SysTick_IRQn 1 */

  /* USER CODE END SysTick_IRQn 1 */
}
 8000318:	bf00      	nop
 800031a:	bd80      	pop	{r7, pc}

0800031c <SystemInit>:
  * @note   This function should be used only after reset.
  * @param  None
  * @retval None
  */
void SystemInit (void)
{
 800031c:	b480      	push	{r7}
 800031e:	af00      	add	r7, sp, #0

  /* Configure the Vector Table location -------------------------------------*/
#if defined(USER_VECT_TAB_ADDRESS)
  SCB->VTOR = VECT_TAB_BASE_ADDRESS | VECT_TAB_OFFSET; /* Vector Table Relocation in Internal SRAM. */
#endif /* USER_VECT_TAB_ADDRESS */
}
 8000320:	bf00      	nop
 8000322:	46bd      	mov	sp, r7
 8000324:	bc80      	pop	{r7}
 8000326:	4770      	bx	lr

08000328 <Reset_Handler>:
  .weak Reset_Handler
  .type Reset_Handler, %function
Reset_Handler:

/* Call the clock system initialization function.*/
    bl  SystemInit
 8000328:	f7ff fff8 	bl	800031c <SystemInit>

/* Copy the data segment initializers from flash to SRAM */
  ldr r0, =_sdata
 800032c:	480b      	ldr	r0, [pc, #44]	@ (800035c <LoopFillZerobss+0xe>)
  ldr r1, =_edata
 800032e:	490c      	ldr	r1, [pc, #48]	@ (8000360 <LoopFillZerobss+0x12>)
  ldr r2, =_sidata
 8000330:	4a0c      	ldr	r2, [pc, #48]	@ (8000364 <LoopFillZerobss+0x16>)
  movs r3, #0
 8000332:	2300      	movs	r3, #0
  b LoopCopyDataInit
 8000334:	e002      	b.n	800033c <LoopCopyDataInit>

08000336 <CopyDataInit>:

CopyDataInit:
  ldr r4, [r2, r3]
 8000336:	58d4      	ldr	r4, [r2, r3]
  str r4, [r0, r3]
 8000338:	50c4      	str	r4, [r0, r3]
  adds r3, r3, #4
 800033a:	3304      	adds	r3, #4

0800033c <LoopCopyDataInit>:

LoopCopyDataInit:
  adds r4, r0, r3
 800033c:	18c4      	adds	r4, r0, r3
  cmp r4, r1
 800033e:	428c      	cmp	r4, r1
  bcc CopyDataInit
 8000340:	d3f9      	bcc.n	8000336 <CopyDataInit>
  
/* Zero fill the bss segment. */
  ldr r2, =_sbss
 8000342:	4a09      	ldr	r2, [pc, #36]	@ (8000368 <LoopFillZerobss+0x1a>)
  ldr r4, =_ebss
 8000344:	4c09      	ldr	r4, [pc, #36]	@ (800036c <LoopFillZerobss+0x1e>)
  movs r3, #0
 8000346:	2300      	movs	r3, #0
  b LoopFillZerobss
 8000348:	e001      	b.n	800034e <LoopFillZerobss>

0800034a <FillZerobss>:

FillZerobss:
  str  r3, [r2]
 800034a:	6013      	str	r3, [r2, #0]
  adds r2, r2, #4
 800034c:	3204      	adds	r2, #4

0800034e <LoopFillZerobss>:

LoopFillZerobss:
  cmp r2, r4
 800034e:	42a2      	cmp	r2, r4
  bcc FillZerobss
 8000350:	d3fb      	bcc.n	800034a <FillZerobss>

/* Call static constructors */
    bl __libc_init_array
 8000352:	f000 fed7 	bl	8001104 <__libc_init_array>
/* Call the application's entry point.*/
  bl main
 8000356:	f7ff fef9 	bl	800014c <main>
  bx lr
 800035a:	4770      	bx	lr
  ldr r0, =_sdata
 800035c:	20000000 	.word	0x20000000
  ldr r1, =_edata
 8000360:	2000000c 	.word	0x2000000c
  ldr r2, =_sidata
 8000364:	08001190 	.word	0x08001190
  ldr r2, =_sbss
 8000368:	2000000c 	.word	0x2000000c
  ldr r4, =_ebss
 800036c:	2000002c 	.word	0x2000002c

08000370 <ADC1_2_IRQHandler>:
 * @retval : None
*/
    .section .text.Default_Handler,"ax",%progbits
Default_Handler:
Infinite_Loop:
  b Infinite_Loop
 8000370:	e7fe      	b.n	8000370 <ADC1_2_IRQHandler>
	...

08000374 <HAL_Init>:
  *         need to ensure that the SysTick time base is always set to 1 millisecond
  *         to have correct HAL operation.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_Init(void)
{
 8000374:	b580      	push	{r7, lr}
 8000376:	af00      	add	r7, sp, #0
    defined(STM32F102x6) || defined(STM32F102xB) || \
    defined(STM32F103x6) || defined(STM32F103xB) || defined(STM32F103xE) || defined(STM32F103xG) || \
    defined(STM32F105xC) || defined(STM32F107xC)

  /* Prefetch buffer is not available on value line devices */
  __HAL_FLASH_PREFETCH_BUFFER_ENABLE();
 8000378:	4b08      	ldr	r3, [pc, #32]	@ (800039c <HAL_Init+0x28>)
 800037a:	681b      	ldr	r3, [r3, #0]
 800037c:	4a07      	ldr	r2, [pc, #28]	@ (800039c <HAL_Init+0x28>)
 800037e:	f043 0310 	orr.w	r3, r3, #16
 8000382:	6013      	str	r3, [r2, #0]
#endif
#endif /* PREFETCH_ENABLE */

  /* Set Interrupt Group Priority */
  HAL_NVIC_SetPriorityGrouping(NVIC_PRIORITYGROUP_4);
 8000384:	2003      	movs	r0, #3
 8000386:	f000 f907 	bl	8000598 <HAL_NVIC_SetPriorityGrouping>

  /* Use systick as time base source and configure 1ms tick (default clock after Reset is HSI) */
  HAL_InitTick(TICK_INT_PRIORITY);
 800038a:	200f      	movs	r0, #15
 800038c:	f000 f808 	bl	80003a0 <HAL_InitTick>

  /* Init the low level hardware */
  HAL_MspInit();
 8000390:	f7ff ff76 	bl	8000280 <HAL_MspInit>

  /* Return function status */
  return HAL_OK;
 8000394:	2300      	movs	r3, #0
}
 8000396:	4618      	mov	r0, r3
 8000398:	bd80      	pop	{r7, pc}
 800039a:	bf00      	nop
 800039c:	40022000 	.word	0x40022000

080003a0 <HAL_InitTick>:
  *       implementation  in user file.
  * @param TickPriority Tick interrupt priority.
  * @retval HAL status
  */
__weak HAL_StatusTypeDef HAL_InitTick(uint32_t TickPriority)
{
 80003a0:	b580      	push	{r7, lr}
 80003a2:	b082      	sub	sp, #8
 80003a4:	af00      	add	r7, sp, #0
 80003a6:	6078      	str	r0, [r7, #4]
  /* Configure the SysTick to have interrupt in 1ms time basis*/
  if (HAL_SYSTICK_Config(SystemCoreClock / (1000U / uwTickFreq)) > 0U)
 80003a8:	4b12      	ldr	r3, [pc, #72]	@ (80003f4 <HAL_InitTick+0x54>)
 80003aa:	681a      	ldr	r2, [r3, #0]
 80003ac:	4b12      	ldr	r3, [pc, #72]	@ (80003f8 <HAL_InitTick+0x58>)
 80003ae:	781b      	ldrb	r3, [r3, #0]
 80003b0:	4619      	mov	r1, r3
 80003b2:	f44f 737a 	mov.w	r3, #1000	@ 0x3e8
 80003b6:	fbb3 f3f1 	udiv	r3, r3, r1
 80003ba:	fbb2 f3f3 	udiv	r3, r2, r3
 80003be:	4618      	mov	r0, r3
 80003c0:	f000 f911 	bl	80005e6 <HAL_SYSTICK_Config>
 80003c4:	4603      	mov	r3, r0
 80003c6:	2b00      	cmp	r3, #0
 80003c8:	d001      	beq.n	80003ce <HAL_InitTick+0x2e>
  {
    return HAL_ERROR;
 80003ca:	2301      	movs	r3, #1
 80003cc:	e00e      	b.n	80003ec <HAL_InitTick+0x4c>
  }

  /* Configure the SysTick IRQ priority */
  if (TickPriority < (1UL << __NVIC_PRIO_BITS))
 80003ce:	687b      	ldr	r3, [r7, #4]
 80003d0:	2b0f      	cmp	r3, #15
 80003d2:	d80a      	bhi.n	80003ea <HAL_InitTick+0x4a>
  {
    HAL_NVIC_SetPriority(SysTick_IRQn, TickPriority, 0U);
 80003d4:	2200      	movs	r2, #0
 80003d6:	6879      	ldr	r1, [r7, #4]
 80003d8:	f04f 30ff 	mov.w	r0, #4294967295	@ 0xffffffff
 80003dc:	f000 f8e7 	bl	80005ae <HAL_NVIC_SetPriority>
    uwTickPrio = TickPriority;
 80003e0:	4a06      	ldr	r2, [pc, #24]	@ (80003fc <HAL_InitTick+0x5c>)
 80003e2:	687b      	ldr	r3, [r7, #4]
 80003e4:	6013      	str	r3, [r2, #0]
  {
    return HAL_ERROR;
  }

  /* Return function status */
  return HAL_OK;
 80003e6:	2300      	movs	r3, #0
 80003e8:	e000      	b.n	80003ec <HAL_InitTick+0x4c>
    return HAL_ERROR;
 80003ea:	2301      	movs	r3, #1
}
 80003ec:	4618      	mov	r0, r3
 80003ee:	3708      	adds	r7, #8
 80003f0:	46bd      	mov	sp, r7
 80003f2:	bd80      	pop	{r7, pc}
 80003f4:	20000000 	.word	0x20000000
 80003f8:	20000008 	.word	0x20000008
 80003fc:	20000004 	.word	0x20000004

08000400 <HAL_IncTick>:
  * @note This function is declared as __weak to be overwritten in case of other
  *      implementations in user file.
  * @retval None
  */
__weak void HAL_IncTick(void)
{
 8000400:	b480      	push	{r7}
 8000402:	af00      	add	r7, sp, #0
  uwTick += uwTickFreq;
 8000404:	4b05      	ldr	r3, [pc, #20]	@ (800041c <HAL_IncTick+0x1c>)
 8000406:	781b      	ldrb	r3, [r3, #0]
 8000408:	461a      	mov	r2, r3
 800040a:	4b05      	ldr	r3, [pc, #20]	@ (8000420 <HAL_IncTick+0x20>)
 800040c:	681b      	ldr	r3, [r3, #0]
 800040e:	4413      	add	r3, r2
 8000410:	4a03      	ldr	r2, [pc, #12]	@ (8000420 <HAL_IncTick+0x20>)
 8000412:	6013      	str	r3, [r2, #0]
}
 8000414:	bf00      	nop
 8000416:	46bd      	mov	sp, r7
 8000418:	bc80      	pop	{r7}
 800041a:	4770      	bx	lr
 800041c:	20000008 	.word	0x20000008
 8000420:	20000028 	.word	0x20000028

08000424 <HAL_GetTick>:
  * @note  This function is declared as __weak to be overwritten in case of other
  *       implementations in user file.
  * @retval tick value
  */
__weak uint32_t HAL_GetTick(void)
{
 8000424:	b480      	push	{r7}
 8000426:	af00      	add	r7, sp, #0
  return uwTick;
 8000428:	4b02      	ldr	r3, [pc, #8]	@ (8000434 <HAL_GetTick+0x10>)
 800042a:	681b      	ldr	r3, [r3, #0]
}
 800042c:	4618      	mov	r0, r3
 800042e:	46bd      	mov	sp, r7
 8000430:	bc80      	pop	{r7}
 8000432:	4770      	bx	lr
 8000434:	20000028 	.word	0x20000028

08000438 <__NVIC_SetPriorityGrouping>:
           In case of a conflict between priority grouping and available
           priority bits (__NVIC_PRIO_BITS), the smallest possible priority group is set.
  \param [in]      PriorityGroup  Priority grouping field.
 */
__STATIC_INLINE void __NVIC_SetPriorityGrouping(uint32_t PriorityGroup)
{
 8000438:	b480      	push	{r7}
 800043a:	b085      	sub	sp, #20
 800043c:	af00      	add	r7, sp, #0
 800043e:	6078      	str	r0, [r7, #4]
  uint32_t reg_value;
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);             /* only values 0..7 are used          */
 8000440:	687b      	ldr	r3, [r7, #4]
 8000442:	f003 0307 	and.w	r3, r3, #7
 8000446:	60fb      	str	r3, [r7, #12]

  reg_value  =  SCB->AIRCR;                                                   /* read old register configuration    */
 8000448:	4b0c      	ldr	r3, [pc, #48]	@ (800047c <__NVIC_SetPriorityGrouping+0x44>)
 800044a:	68db      	ldr	r3, [r3, #12]
 800044c:	60bb      	str	r3, [r7, #8]
  reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to change               */
 800044e:	68ba      	ldr	r2, [r7, #8]
 8000450:	f64f 03ff 	movw	r3, #63743	@ 0xf8ff
 8000454:	4013      	ands	r3, r2
 8000456:	60bb      	str	r3, [r7, #8]
  reg_value  =  (reg_value                                   |
                ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
                (PriorityGroupTmp << SCB_AIRCR_PRIGROUP_Pos) );               /* Insert write key and priority group */
 8000458:	68fb      	ldr	r3, [r7, #12]
 800045a:	021a      	lsls	r2, r3, #8
                ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
 800045c:	68bb      	ldr	r3, [r7, #8]
 800045e:	4313      	orrs	r3, r2
  reg_value  =  (reg_value                                   |
 8000460:	f043 63bf 	orr.w	r3, r3, #100139008	@ 0x5f80000
 8000464:	f443 3300 	orr.w	r3, r3, #131072	@ 0x20000
 8000468:	60bb      	str	r3, [r7, #8]
  SCB->AIRCR =  reg_value;
 800046a:	4a04      	ldr	r2, [pc, #16]	@ (800047c <__NVIC_SetPriorityGrouping+0x44>)
 800046c:	68bb      	ldr	r3, [r7, #8]
 800046e:	60d3      	str	r3, [r2, #12]
}
 8000470:	bf00      	nop
 8000472:	3714      	adds	r7, #20
 8000474:	46bd      	mov	sp, r7
 8000476:	bc80      	pop	{r7}
 8000478:	4770      	bx	lr
 800047a:	bf00      	nop
 800047c:	e000ed00 	.word	0xe000ed00

08000480 <__NVIC_GetPriorityGrouping>:
  \brief   Get Priority Grouping
  \details Reads the priority grouping field from the NVIC Interrupt Controller.
  \return                Priority grouping field (SCB->AIRCR [10:8] PRIGROUP field).
 */
__STATIC_INLINE uint32_t __NVIC_GetPriorityGrouping(void)
{
 8000480:	b480      	push	{r7}
 8000482:	af00      	add	r7, sp, #0
  return ((uint32_t)((SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) >> SCB_AIRCR_PRIGROUP_Pos));
 8000484:	4b04      	ldr	r3, [pc, #16]	@ (8000498 <__NVIC_GetPriorityGrouping+0x18>)
 8000486:	68db      	ldr	r3, [r3, #12]
 8000488:	0a1b      	lsrs	r3, r3, #8
 800048a:	f003 0307 	and.w	r3, r3, #7
}
 800048e:	4618      	mov	r0, r3
 8000490:	46bd      	mov	sp, r7
 8000492:	bc80      	pop	{r7}
 8000494:	4770      	bx	lr
 8000496:	bf00      	nop
 8000498:	e000ed00 	.word	0xe000ed00

0800049c <__NVIC_SetPriority>:
  \param [in]      IRQn  Interrupt number.
  \param [in]  priority  Priority to set.
  \note    The priority cannot be set for every processor exception.
 */
__STATIC_INLINE void __NVIC_SetPriority(IRQn_Type IRQn, uint32_t priority)
{
 800049c:	b480      	push	{r7}
 800049e:	b083      	sub	sp, #12
 80004a0:	af00      	add	r7, sp, #0
 80004a2:	4603      	mov	r3, r0
 80004a4:	6039      	str	r1, [r7, #0]
 80004a6:	71fb      	strb	r3, [r7, #7]
  if ((int32_t)(IRQn) >= 0)
 80004a8:	f997 3007 	ldrsb.w	r3, [r7, #7]
 80004ac:	2b00      	cmp	r3, #0
 80004ae:	db0a      	blt.n	80004c6 <__NVIC_SetPriority+0x2a>
  {
    NVIC->IP[((uint32_t)IRQn)]               = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 80004b0:	683b      	ldr	r3, [r7, #0]
 80004b2:	b2da      	uxtb	r2, r3
 80004b4:	490c      	ldr	r1, [pc, #48]	@ (80004e8 <__NVIC_SetPriority+0x4c>)
 80004b6:	f997 3007 	ldrsb.w	r3, [r7, #7]
 80004ba:	0112      	lsls	r2, r2, #4
 80004bc:	b2d2      	uxtb	r2, r2
 80004be:	440b      	add	r3, r1
 80004c0:	f883 2300 	strb.w	r2, [r3, #768]	@ 0x300
  }
  else
  {
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
  }
}
 80004c4:	e00a      	b.n	80004dc <__NVIC_SetPriority+0x40>
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 80004c6:	683b      	ldr	r3, [r7, #0]
 80004c8:	b2da      	uxtb	r2, r3
 80004ca:	4908      	ldr	r1, [pc, #32]	@ (80004ec <__NVIC_SetPriority+0x50>)
 80004cc:	79fb      	ldrb	r3, [r7, #7]
 80004ce:	f003 030f 	and.w	r3, r3, #15
 80004d2:	3b04      	subs	r3, #4
 80004d4:	0112      	lsls	r2, r2, #4
 80004d6:	b2d2      	uxtb	r2, r2
 80004d8:	440b      	add	r3, r1
 80004da:	761a      	strb	r2, [r3, #24]
}
 80004dc:	bf00      	nop
 80004de:	370c      	adds	r7, #12
 80004e0:	46bd      	mov	sp, r7
 80004e2:	bc80      	pop	{r7}
 80004e4:	4770      	bx	lr
 80004e6:	bf00      	nop
 80004e8:	e000e100 	.word	0xe000e100
 80004ec:	e000ed00 	.word	0xe000ed00

080004f0 <NVIC_EncodePriority>:
  \param [in]   PreemptPriority  Preemptive priority value (starting from 0).
  \param [in]       SubPriority  Subpriority value (starting from 0).
  \return                        Encoded priority. Value can be used in the function \ref NVIC_SetPriority().
 */
__STATIC_INLINE uint32_t NVIC_EncodePriority (uint32_t PriorityGroup, uint32_t PreemptPriority, uint32_t SubPriority)
{
 80004f0:	b480      	push	{r7}
 80004f2:	b089      	sub	sp, #36	@ 0x24
 80004f4:	af00      	add	r7, sp, #0
 80004f6:	60f8      	str	r0, [r7, #12]
 80004f8:	60b9      	str	r1, [r7, #8]
 80004fa:	607a      	str	r2, [r7, #4]
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);   /* only values 0..7 are used          */
 80004fc:	68fb      	ldr	r3, [r7, #12]
 80004fe:	f003 0307 	and.w	r3, r3, #7
 8000502:	61fb      	str	r3, [r7, #28]
  uint32_t PreemptPriorityBits;
  uint32_t SubPriorityBits;

  PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(__NVIC_PRIO_BITS)) ? (uint32_t)(__NVIC_PRIO_BITS) : (uint32_t)(7UL - PriorityGroupTmp);
 8000504:	69fb      	ldr	r3, [r7, #28]
 8000506:	f1c3 0307 	rsb	r3, r3, #7
 800050a:	2b04      	cmp	r3, #4
 800050c:	bf28      	it	cs
 800050e:	2304      	movcs	r3, #4
 8000510:	61bb      	str	r3, [r7, #24]
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(__NVIC_PRIO_BITS));
 8000512:	69fb      	ldr	r3, [r7, #28]
 8000514:	3304      	adds	r3, #4
 8000516:	2b06      	cmp	r3, #6
 8000518:	d902      	bls.n	8000520 <NVIC_EncodePriority+0x30>
 800051a:	69fb      	ldr	r3, [r7, #28]
 800051c:	3b03      	subs	r3, #3
 800051e:	e000      	b.n	8000522 <NVIC_EncodePriority+0x32>
 8000520:	2300      	movs	r3, #0
 8000522:	617b      	str	r3, [r7, #20]

  return (
           ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits) |
 8000524:	f04f 32ff 	mov.w	r2, #4294967295	@ 0xffffffff
 8000528:	69bb      	ldr	r3, [r7, #24]
 800052a:	fa02 f303 	lsl.w	r3, r2, r3
 800052e:	43da      	mvns	r2, r3
 8000530:	68bb      	ldr	r3, [r7, #8]
 8000532:	401a      	ands	r2, r3
 8000534:	697b      	ldr	r3, [r7, #20]
 8000536:	409a      	lsls	r2, r3
           ((SubPriority     & (uint32_t)((1UL << (SubPriorityBits    )) - 1UL)))
 8000538:	f04f 31ff 	mov.w	r1, #4294967295	@ 0xffffffff
 800053c:	697b      	ldr	r3, [r7, #20]
 800053e:	fa01 f303 	lsl.w	r3, r1, r3
 8000542:	43d9      	mvns	r1, r3
 8000544:	687b      	ldr	r3, [r7, #4]
 8000546:	400b      	ands	r3, r1
           ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits) |
 8000548:	4313      	orrs	r3, r2
         );
}
 800054a:	4618      	mov	r0, r3
 800054c:	3724      	adds	r7, #36	@ 0x24
 800054e:	46bd      	mov	sp, r7
 8000550:	bc80      	pop	{r7}
 8000552:	4770      	bx	lr

08000554 <SysTick_Config>:
  \note    When the variable <b>__Vendor_SysTickConfig</b> is set to 1, then the
           function <b>SysTick_Config</b> is not included. In this case, the file <b><i>device</i>.h</b>
           must contain a vendor-specific implementation of this function.
 */
__STATIC_INLINE uint32_t SysTick_Config(uint32_t ticks)
{
 8000554:	b580      	push	{r7, lr}
 8000556:	b082      	sub	sp, #8
 8000558:	af00      	add	r7, sp, #0
 800055a:	6078      	str	r0, [r7, #4]
  if ((ticks - 1UL) > SysTick_LOAD_RELOAD_Msk)
 800055c:	687b      	ldr	r3, [r7, #4]
 800055e:	3b01      	subs	r3, #1
 8000560:	f1b3 7f80 	cmp.w	r3, #16777216	@ 0x1000000
 8000564:	d301      	bcc.n	800056a <SysTick_Config+0x16>
  {
    return (1UL);                                                   /* Reload value impossible */
 8000566:	2301      	movs	r3, #1
 8000568:	e00f      	b.n	800058a <SysTick_Config+0x36>
  }

  SysTick->LOAD  = (uint32_t)(ticks - 1UL);                         /* set reload register */
 800056a:	4a0a      	ldr	r2, [pc, #40]	@ (8000594 <SysTick_Config+0x40>)
 800056c:	687b      	ldr	r3, [r7, #4]
 800056e:	3b01      	subs	r3, #1
 8000570:	6053      	str	r3, [r2, #4]
  NVIC_SetPriority (SysTick_IRQn, (1UL << __NVIC_PRIO_BITS) - 1UL); /* set Priority for Systick Interrupt */
 8000572:	210f      	movs	r1, #15
 8000574:	f04f 30ff 	mov.w	r0, #4294967295	@ 0xffffffff
 8000578:	f7ff ff90 	bl	800049c <__NVIC_SetPriority>
  SysTick->VAL   = 0UL;                                             /* Load the SysTick Counter Value */
 800057c:	4b05      	ldr	r3, [pc, #20]	@ (8000594 <SysTick_Config+0x40>)
 800057e:	2200      	movs	r2, #0
 8000580:	609a      	str	r2, [r3, #8]
  SysTick->CTRL  = SysTick_CTRL_CLKSOURCE_Msk |
 8000582:	4b04      	ldr	r3, [pc, #16]	@ (8000594 <SysTick_Config+0x40>)
 8000584:	2207      	movs	r2, #7
 8000586:	601a      	str	r2, [r3, #0]
                   SysTick_CTRL_TICKINT_Msk   |
                   SysTick_CTRL_ENABLE_Msk;                         /* Enable SysTick IRQ and SysTick Timer */
  return (0UL);                                                     /* Function successful */
 8000588:	2300      	movs	r3, #0
}
 800058a:	4618      	mov	r0, r3
 800058c:	3708      	adds	r7, #8
 800058e:	46bd      	mov	sp, r7
 8000590:	bd80      	pop	{r7, pc}
 8000592:	bf00      	nop
 8000594:	e000e010 	.word	0xe000e010

08000598 <HAL_NVIC_SetPriorityGrouping>:
  * @note   When the NVIC_PriorityGroup_0 is selected, IRQ preemption is no more possible. 
  *         The pending IRQ priority will be managed only by the subpriority. 
  * @retval None
  */
void HAL_NVIC_SetPriorityGrouping(uint32_t PriorityGroup)
{
 8000598:	b580      	push	{r7, lr}
 800059a:	b082      	sub	sp, #8
 800059c:	af00      	add	r7, sp, #0
 800059e:	6078      	str	r0, [r7, #4]
  /* Check the parameters */
  assert_param(IS_NVIC_PRIORITY_GROUP(PriorityGroup));
  
  /* Set the PRIGROUP[10:8] bits according to the PriorityGroup parameter value */
  NVIC_SetPriorityGrouping(PriorityGroup);
 80005a0:	6878      	ldr	r0, [r7, #4]
 80005a2:	f7ff ff49 	bl	8000438 <__NVIC_SetPriorityGrouping>
}
 80005a6:	bf00      	nop
 80005a8:	3708      	adds	r7, #8
 80005aa:	46bd      	mov	sp, r7
 80005ac:	bd80      	pop	{r7, pc}

080005ae <HAL_NVIC_SetPriority>:
  *         This parameter can be a value between 0 and 15
  *         A lower priority value indicates a higher priority.          
  * @retval None
  */
void HAL_NVIC_SetPriority(IRQn_Type IRQn, uint32_t PreemptPriority, uint32_t SubPriority)
{ 
 80005ae:	b580      	push	{r7, lr}
 80005b0:	b086      	sub	sp, #24
 80005b2:	af00      	add	r7, sp, #0
 80005b4:	4603      	mov	r3, r0
 80005b6:	60b9      	str	r1, [r7, #8]
 80005b8:	607a      	str	r2, [r7, #4]
 80005ba:	73fb      	strb	r3, [r7, #15]
  uint32_t prioritygroup = 0x00U;
 80005bc:	2300      	movs	r3, #0
 80005be:	617b      	str	r3, [r7, #20]
  
  /* Check the parameters */
  assert_param(IS_NVIC_SUB_PRIORITY(SubPriority));
  assert_param(IS_NVIC_PREEMPTION_PRIORITY(PreemptPriority));
  
  prioritygroup = NVIC_GetPriorityGrouping();
 80005c0:	f7ff ff5e 	bl	8000480 <__NVIC_GetPriorityGrouping>
 80005c4:	6178      	str	r0, [r7, #20]
  
  NVIC_SetPriority(IRQn, NVIC_EncodePriority(prioritygroup, PreemptPriority, SubPriority));
 80005c6:	687a      	ldr	r2, [r7, #4]
 80005c8:	68b9      	ldr	r1, [r7, #8]
 80005ca:	6978      	ldr	r0, [r7, #20]
 80005cc:	f7ff ff90 	bl	80004f0 <NVIC_EncodePriority>
 80005d0:	4602      	mov	r2, r0
 80005d2:	f997 300f 	ldrsb.w	r3, [r7, #15]
 80005d6:	4611      	mov	r1, r2
 80005d8:	4618      	mov	r0, r3
 80005da:	f7ff ff5f 	bl	800049c <__NVIC_SetPriority>
}
 80005de:	bf00      	nop
 80005e0:	3718      	adds	r7, #24
 80005e2:	46bd      	mov	sp, r7
 80005e4:	bd80      	pop	{r7, pc}

080005e6 <HAL_SYSTICK_Config>:
  * @param  TicksNumb: Specifies the ticks Number of ticks between two interrupts.
  * @retval status:  - 0  Function succeeded.
  *                  - 1  Function failed.
  */
uint32_t HAL_SYSTICK_Config(uint32_t TicksNumb)
{
 80005e6:	b580      	push	{r7, lr}
 80005e8:	b082      	sub	sp, #8
 80005ea:	af00      	add	r7, sp, #0
 80005ec:	6078      	str	r0, [r7, #4]
   return SysTick_Config(TicksNumb);
 80005ee:	6878      	ldr	r0, [r7, #4]
 80005f0:	f7ff ffb0 	bl	8000554 <SysTick_Config>
 80005f4:	4603      	mov	r3, r0
}
 80005f6:	4618      	mov	r0, r3
 80005f8:	3708      	adds	r7, #8
 80005fa:	46bd      	mov	sp, r7
 80005fc:	bd80      	pop	{r7, pc}
	...

08000600 <HAL_GPIO_Init>:
  * @param  GPIO_Init: pointer to a GPIO_InitTypeDef structure that contains
  *         the configuration information for the specified GPIO peripheral.
  * @retval None
  */
void HAL_GPIO_Init(GPIO_TypeDef  *GPIOx, GPIO_InitTypeDef *GPIO_Init)
{
 8000600:	b480      	push	{r7}
 8000602:	b08b      	sub	sp, #44	@ 0x2c
 8000604:	af00      	add	r7, sp, #0
 8000606:	6078      	str	r0, [r7, #4]
 8000608:	6039      	str	r1, [r7, #0]
  uint32_t position = 0x00u;
 800060a:	2300      	movs	r3, #0
 800060c:	627b      	str	r3, [r7, #36]	@ 0x24
  uint32_t ioposition;
  uint32_t iocurrent;
  uint32_t temp;
  uint32_t config = 0x00u;
 800060e:	2300      	movs	r3, #0
 8000610:	623b      	str	r3, [r7, #32]
  assert_param(IS_GPIO_ALL_INSTANCE(GPIOx));
  assert_param(IS_GPIO_PIN(GPIO_Init->Pin));
  assert_param(IS_GPIO_MODE(GPIO_Init->Mode));

  /* Configure the port pins */
  while (((GPIO_Init->Pin) >> position) != 0x00u)
 8000612:	e169      	b.n	80008e8 <HAL_GPIO_Init+0x2e8>
  {
    /* Get the IO position */
    ioposition = (0x01uL << position);
 8000614:	2201      	movs	r2, #1
 8000616:	6a7b      	ldr	r3, [r7, #36]	@ 0x24
 8000618:	fa02 f303 	lsl.w	r3, r2, r3
 800061c:	61fb      	str	r3, [r7, #28]

    /* Get the current IO position */
    iocurrent = (uint32_t)(GPIO_Init->Pin) & ioposition;
 800061e:	683b      	ldr	r3, [r7, #0]
 8000620:	681b      	ldr	r3, [r3, #0]
 8000622:	69fa      	ldr	r2, [r7, #28]
 8000624:	4013      	ands	r3, r2
 8000626:	61bb      	str	r3, [r7, #24]

    if (iocurrent == ioposition)
 8000628:	69ba      	ldr	r2, [r7, #24]
 800062a:	69fb      	ldr	r3, [r7, #28]
 800062c:	429a      	cmp	r2, r3
 800062e:	f040 8158 	bne.w	80008e2 <HAL_GPIO_Init+0x2e2>
    {
      /* Check the Alternate function parameters */
      assert_param(IS_GPIO_AF_INSTANCE(GPIOx));

      /* Based on the required mode, filling config variable with MODEy[1:0] and CNFy[3:2] corresponding bits */
      switch (GPIO_Init->Mode)
 8000632:	683b      	ldr	r3, [r7, #0]
 8000634:	685b      	ldr	r3, [r3, #4]
 8000636:	4a9a      	ldr	r2, [pc, #616]	@ (80008a0 <HAL_GPIO_Init+0x2a0>)
 8000638:	4293      	cmp	r3, r2
 800063a:	d05e      	beq.n	80006fa <HAL_GPIO_Init+0xfa>
 800063c:	4a98      	ldr	r2, [pc, #608]	@ (80008a0 <HAL_GPIO_Init+0x2a0>)
 800063e:	4293      	cmp	r3, r2
 8000640:	d875      	bhi.n	800072e <HAL_GPIO_Init+0x12e>
 8000642:	4a98      	ldr	r2, [pc, #608]	@ (80008a4 <HAL_GPIO_Init+0x2a4>)
 8000644:	4293      	cmp	r3, r2
 8000646:	d058      	beq.n	80006fa <HAL_GPIO_Init+0xfa>
 8000648:	4a96      	ldr	r2, [pc, #600]	@ (80008a4 <HAL_GPIO_Init+0x2a4>)
 800064a:	4293      	cmp	r3, r2
 800064c:	d86f      	bhi.n	800072e <HAL_GPIO_Init+0x12e>
 800064e:	4a96      	ldr	r2, [pc, #600]	@ (80008a8 <HAL_GPIO_Init+0x2a8>)
 8000650:	4293      	cmp	r3, r2
 8000652:	d052      	beq.n	80006fa <HAL_GPIO_Init+0xfa>
 8000654:	4a94      	ldr	r2, [pc, #592]	@ (80008a8 <HAL_GPIO_Init+0x2a8>)
 8000656:	4293      	cmp	r3, r2
 8000658:	d869      	bhi.n	800072e <HAL_GPIO_Init+0x12e>
 800065a:	4a94      	ldr	r2, [pc, #592]	@ (80008ac <HAL_GPIO_Init+0x2ac>)
 800065c:	4293      	cmp	r3, r2
 800065e:	d04c      	beq.n	80006fa <HAL_GPIO_Init+0xfa>
 8000660:	4a92      	ldr	r2, [pc, #584]	@ (80008ac <HAL_GPIO_Init+0x2ac>)
 8000662:	4293      	cmp	r3, r2
 8000664:	d863      	bhi.n	800072e <HAL_GPIO_Init+0x12e>
 8000666:	4a92      	ldr	r2, [pc, #584]	@ (80008b0 <HAL_GPIO_Init+0x2b0>)
 8000668:	4293      	cmp	r3, r2
 800066a:	d046      	beq.n	80006fa <HAL_GPIO_Init+0xfa>
 800066c:	4a90      	ldr	r2, [pc, #576]	@ (80008b0 <HAL_GPIO_Init+0x2b0>)
 800066e:	4293      	cmp	r3, r2
 8000670:	d85d      	bhi.n	800072e <HAL_GPIO_Init+0x12e>
 8000672:	2b12      	cmp	r3, #18
 8000674:	d82a      	bhi.n	80006cc <HAL_GPIO_Init+0xcc>
 8000676:	2b12      	cmp	r3, #18
 8000678:	d859      	bhi.n	800072e <HAL_GPIO_Init+0x12e>
 800067a:	a201      	add	r2, pc, #4	@ (adr r2, 8000680 <HAL_GPIO_Init+0x80>)
 800067c:	f852 f023 	ldr.w	pc, [r2, r3, lsl #2]
 8000680:	080006fb 	.word	0x080006fb
 8000684:	080006d5 	.word	0x080006d5
 8000688:	080006e7 	.word	0x080006e7
 800068c:	08000729 	.word	0x08000729
 8000690:	0800072f 	.word	0x0800072f
 8000694:	0800072f 	.word	0x0800072f
 8000698:	0800072f 	.word	0x0800072f
 800069c:	0800072f 	.word	0x0800072f
 80006a0:	0800072f 	.word	0x0800072f
 80006a4:	0800072f 	.word	0x0800072f
 80006a8:	0800072f 	.word	0x0800072f
 80006ac:	0800072f 	.word	0x0800072f
 80006b0:	0800072f 	.word	0x0800072f
 80006b4:	0800072f 	.word	0x0800072f
 80006b8:	0800072f 	.word	0x0800072f
 80006bc:	0800072f 	.word	0x0800072f
 80006c0:	0800072f 	.word	0x0800072f
 80006c4:	080006dd 	.word	0x080006dd
 80006c8:	080006f1 	.word	0x080006f1
 80006cc:	4a79      	ldr	r2, [pc, #484]	@ (80008b4 <HAL_GPIO_Init+0x2b4>)
 80006ce:	4293      	cmp	r3, r2
 80006d0:	d013      	beq.n	80006fa <HAL_GPIO_Init+0xfa>
          config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_ANALOG;
          break;

        /* Parameters are checked with assert_param */
        default:
          break;
 80006d2:	e02c      	b.n	800072e <HAL_GPIO_Init+0x12e>
          config = GPIO_Init->Speed + GPIO_CR_CNF_GP_OUTPUT_PP;
 80006d4:	683b      	ldr	r3, [r7, #0]
 80006d6:	68db      	ldr	r3, [r3, #12]
 80006d8:	623b      	str	r3, [r7, #32]
          break;
 80006da:	e029      	b.n	8000730 <HAL_GPIO_Init+0x130>
          config = GPIO_Init->Speed + GPIO_CR_CNF_GP_OUTPUT_OD;
 80006dc:	683b      	ldr	r3, [r7, #0]
 80006de:	68db      	ldr	r3, [r3, #12]
 80006e0:	3304      	adds	r3, #4
 80006e2:	623b      	str	r3, [r7, #32]
          break;
 80006e4:	e024      	b.n	8000730 <HAL_GPIO_Init+0x130>
          config = GPIO_Init->Speed + GPIO_CR_CNF_AF_OUTPUT_PP;
 80006e6:	683b      	ldr	r3, [r7, #0]
 80006e8:	68db      	ldr	r3, [r3, #12]
 80006ea:	3308      	adds	r3, #8
 80006ec:	623b      	str	r3, [r7, #32]
          break;
 80006ee:	e01f      	b.n	8000730 <HAL_GPIO_Init+0x130>
          config = GPIO_Init->Speed + GPIO_CR_CNF_AF_OUTPUT_OD;
 80006f0:	683b      	ldr	r3, [r7, #0]
 80006f2:	68db      	ldr	r3, [r3, #12]
 80006f4:	330c      	adds	r3, #12
 80006f6:	623b      	str	r3, [r7, #32]
          break;
 80006f8:	e01a      	b.n	8000730 <HAL_GPIO_Init+0x130>
          if (GPIO_Init->Pull == GPIO_NOPULL)
 80006fa:	683b      	ldr	r3, [r7, #0]
 80006fc:	689b      	ldr	r3, [r3, #8]
 80006fe:	2b00      	cmp	r3, #0
 8000700:	d102      	bne.n	8000708 <HAL_GPIO_Init+0x108>
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_FLOATING;
 8000702:	2304      	movs	r3, #4
 8000704:	623b      	str	r3, [r7, #32]
          break;
 8000706:	e013      	b.n	8000730 <HAL_GPIO_Init+0x130>
          else if (GPIO_Init->Pull == GPIO_PULLUP)
 8000708:	683b      	ldr	r3, [r7, #0]
 800070a:	689b      	ldr	r3, [r3, #8]
 800070c:	2b01      	cmp	r3, #1
 800070e:	d105      	bne.n	800071c <HAL_GPIO_Init+0x11c>
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_PU_PD;
 8000710:	2308      	movs	r3, #8
 8000712:	623b      	str	r3, [r7, #32]
            GPIOx->BSRR = ioposition;
 8000714:	687b      	ldr	r3, [r7, #4]
 8000716:	69fa      	ldr	r2, [r7, #28]
 8000718:	611a      	str	r2, [r3, #16]
          break;
 800071a:	e009      	b.n	8000730 <HAL_GPIO_Init+0x130>
            config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_INPUT_PU_PD;
 800071c:	2308      	movs	r3, #8
 800071e:	623b      	str	r3, [r7, #32]
            GPIOx->BRR = ioposition;
 8000720:	687b      	ldr	r3, [r7, #4]
 8000722:	69fa      	ldr	r2, [r7, #28]
 8000724:	615a      	str	r2, [r3, #20]
          break;
 8000726:	e003      	b.n	8000730 <HAL_GPIO_Init+0x130>
          config = GPIO_CR_MODE_INPUT + GPIO_CR_CNF_ANALOG;
 8000728:	2300      	movs	r3, #0
 800072a:	623b      	str	r3, [r7, #32]
          break;
 800072c:	e000      	b.n	8000730 <HAL_GPIO_Init+0x130>
          break;
 800072e:	bf00      	nop
      }

      /* Check if the current bit belongs to first half or last half of the pin count number
       in order to address CRH or CRL register*/
      configregister = (iocurrent < GPIO_PIN_8) ? &GPIOx->CRL     : &GPIOx->CRH;
 8000730:	69bb      	ldr	r3, [r7, #24]
 8000732:	2bff      	cmp	r3, #255	@ 0xff
 8000734:	d801      	bhi.n	800073a <HAL_GPIO_Init+0x13a>
 8000736:	687b      	ldr	r3, [r7, #4]
 8000738:	e001      	b.n	800073e <HAL_GPIO_Init+0x13e>
 800073a:	687b      	ldr	r3, [r7, #4]
 800073c:	3304      	adds	r3, #4
 800073e:	617b      	str	r3, [r7, #20]
      registeroffset = (iocurrent < GPIO_PIN_8) ? (position << 2u) : ((position - 8u) << 2u);
 8000740:	69bb      	ldr	r3, [r7, #24]
 8000742:	2bff      	cmp	r3, #255	@ 0xff
 8000744:	d802      	bhi.n	800074c <HAL_GPIO_Init+0x14c>
 8000746:	6a7b      	ldr	r3, [r7, #36]	@ 0x24
 8000748:	009b      	lsls	r3, r3, #2
 800074a:	e002      	b.n	8000752 <HAL_GPIO_Init+0x152>
 800074c:	6a7b      	ldr	r3, [r7, #36]	@ 0x24
 800074e:	3b08      	subs	r3, #8
 8000750:	009b      	lsls	r3, r3, #2
 8000752:	613b      	str	r3, [r7, #16]

      /* Apply the new configuration of the pin to the register */
      MODIFY_REG((*configregister), ((GPIO_CRL_MODE0 | GPIO_CRL_CNF0) << registeroffset), (config << registeroffset));
 8000754:	697b      	ldr	r3, [r7, #20]
 8000756:	681a      	ldr	r2, [r3, #0]
 8000758:	210f      	movs	r1, #15
 800075a:	693b      	ldr	r3, [r7, #16]
 800075c:	fa01 f303 	lsl.w	r3, r1, r3
 8000760:	43db      	mvns	r3, r3
 8000762:	401a      	ands	r2, r3
 8000764:	6a39      	ldr	r1, [r7, #32]
 8000766:	693b      	ldr	r3, [r7, #16]
 8000768:	fa01 f303 	lsl.w	r3, r1, r3
 800076c:	431a      	orrs	r2, r3
 800076e:	697b      	ldr	r3, [r7, #20]
 8000770:	601a      	str	r2, [r3, #0]

      /*--------------------- EXTI Mode Configuration ------------------------*/
      /* Configure the External Interrupt or event for the current IO */
      if ((GPIO_Init->Mode & EXTI_MODE) == EXTI_MODE)
 8000772:	683b      	ldr	r3, [r7, #0]
 8000774:	685b      	ldr	r3, [r3, #4]
 8000776:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 800077a:	2b00      	cmp	r3, #0
 800077c:	f000 80b1 	beq.w	80008e2 <HAL_GPIO_Init+0x2e2>
      {
        /* Enable AFIO Clock */
        __HAL_RCC_AFIO_CLK_ENABLE();
 8000780:	4b4d      	ldr	r3, [pc, #308]	@ (80008b8 <HAL_GPIO_Init+0x2b8>)
 8000782:	699b      	ldr	r3, [r3, #24]
 8000784:	4a4c      	ldr	r2, [pc, #304]	@ (80008b8 <HAL_GPIO_Init+0x2b8>)
 8000786:	f043 0301 	orr.w	r3, r3, #1
 800078a:	6193      	str	r3, [r2, #24]
 800078c:	4b4a      	ldr	r3, [pc, #296]	@ (80008b8 <HAL_GPIO_Init+0x2b8>)
 800078e:	699b      	ldr	r3, [r3, #24]
 8000790:	f003 0301 	and.w	r3, r3, #1
 8000794:	60bb      	str	r3, [r7, #8]
 8000796:	68bb      	ldr	r3, [r7, #8]
        temp = AFIO->EXTICR[position >> 2u];
 8000798:	4a48      	ldr	r2, [pc, #288]	@ (80008bc <HAL_GPIO_Init+0x2bc>)
 800079a:	6a7b      	ldr	r3, [r7, #36]	@ 0x24
 800079c:	089b      	lsrs	r3, r3, #2
 800079e:	3302      	adds	r3, #2
 80007a0:	f852 3023 	ldr.w	r3, [r2, r3, lsl #2]
 80007a4:	60fb      	str	r3, [r7, #12]
        CLEAR_BIT(temp, (0x0Fu) << (4u * (position & 0x03u)));
 80007a6:	6a7b      	ldr	r3, [r7, #36]	@ 0x24
 80007a8:	f003 0303 	and.w	r3, r3, #3
 80007ac:	009b      	lsls	r3, r3, #2
 80007ae:	220f      	movs	r2, #15
 80007b0:	fa02 f303 	lsl.w	r3, r2, r3
 80007b4:	43db      	mvns	r3, r3
 80007b6:	68fa      	ldr	r2, [r7, #12]
 80007b8:	4013      	ands	r3, r2
 80007ba:	60fb      	str	r3, [r7, #12]
        SET_BIT(temp, (GPIO_GET_INDEX(GPIOx)) << (4u * (position & 0x03u)));
 80007bc:	687b      	ldr	r3, [r7, #4]
 80007be:	4a40      	ldr	r2, [pc, #256]	@ (80008c0 <HAL_GPIO_Init+0x2c0>)
 80007c0:	4293      	cmp	r3, r2
 80007c2:	d013      	beq.n	80007ec <HAL_GPIO_Init+0x1ec>
 80007c4:	687b      	ldr	r3, [r7, #4]
 80007c6:	4a3f      	ldr	r2, [pc, #252]	@ (80008c4 <HAL_GPIO_Init+0x2c4>)
 80007c8:	4293      	cmp	r3, r2
 80007ca:	d00d      	beq.n	80007e8 <HAL_GPIO_Init+0x1e8>
 80007cc:	687b      	ldr	r3, [r7, #4]
 80007ce:	4a3e      	ldr	r2, [pc, #248]	@ (80008c8 <HAL_GPIO_Init+0x2c8>)
 80007d0:	4293      	cmp	r3, r2
 80007d2:	d007      	beq.n	80007e4 <HAL_GPIO_Init+0x1e4>
 80007d4:	687b      	ldr	r3, [r7, #4]
 80007d6:	4a3d      	ldr	r2, [pc, #244]	@ (80008cc <HAL_GPIO_Init+0x2cc>)
 80007d8:	4293      	cmp	r3, r2
 80007da:	d101      	bne.n	80007e0 <HAL_GPIO_Init+0x1e0>
 80007dc:	2303      	movs	r3, #3
 80007de:	e006      	b.n	80007ee <HAL_GPIO_Init+0x1ee>
 80007e0:	2304      	movs	r3, #4
 80007e2:	e004      	b.n	80007ee <HAL_GPIO_Init+0x1ee>
 80007e4:	2302      	movs	r3, #2
 80007e6:	e002      	b.n	80007ee <HAL_GPIO_Init+0x1ee>
 80007e8:	2301      	movs	r3, #1
 80007ea:	e000      	b.n	80007ee <HAL_GPIO_Init+0x1ee>
 80007ec:	2300      	movs	r3, #0
 80007ee:	6a7a      	ldr	r2, [r7, #36]	@ 0x24
 80007f0:	f002 0203 	and.w	r2, r2, #3
 80007f4:	0092      	lsls	r2, r2, #2
 80007f6:	4093      	lsls	r3, r2
 80007f8:	68fa      	ldr	r2, [r7, #12]
 80007fa:	4313      	orrs	r3, r2
 80007fc:	60fb      	str	r3, [r7, #12]
        AFIO->EXTICR[position >> 2u] = temp;
 80007fe:	492f      	ldr	r1, [pc, #188]	@ (80008bc <HAL_GPIO_Init+0x2bc>)
 8000800:	6a7b      	ldr	r3, [r7, #36]	@ 0x24
 8000802:	089b      	lsrs	r3, r3, #2
 8000804:	3302      	adds	r3, #2
 8000806:	68fa      	ldr	r2, [r7, #12]
 8000808:	f841 2023 	str.w	r2, [r1, r3, lsl #2]


        /* Enable or disable the rising trigger */
        if ((GPIO_Init->Mode & RISING_EDGE) == RISING_EDGE)
 800080c:	683b      	ldr	r3, [r7, #0]
 800080e:	685b      	ldr	r3, [r3, #4]
 8000810:	f403 1380 	and.w	r3, r3, #1048576	@ 0x100000
 8000814:	2b00      	cmp	r3, #0
 8000816:	d006      	beq.n	8000826 <HAL_GPIO_Init+0x226>
        {
          SET_BIT(EXTI->RTSR, iocurrent);
 8000818:	4b2d      	ldr	r3, [pc, #180]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 800081a:	689a      	ldr	r2, [r3, #8]
 800081c:	492c      	ldr	r1, [pc, #176]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 800081e:	69bb      	ldr	r3, [r7, #24]
 8000820:	4313      	orrs	r3, r2
 8000822:	608b      	str	r3, [r1, #8]
 8000824:	e006      	b.n	8000834 <HAL_GPIO_Init+0x234>
        }
        else
        {
          CLEAR_BIT(EXTI->RTSR, iocurrent);
 8000826:	4b2a      	ldr	r3, [pc, #168]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 8000828:	689a      	ldr	r2, [r3, #8]
 800082a:	69bb      	ldr	r3, [r7, #24]
 800082c:	43db      	mvns	r3, r3
 800082e:	4928      	ldr	r1, [pc, #160]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 8000830:	4013      	ands	r3, r2
 8000832:	608b      	str	r3, [r1, #8]
        }

        /* Enable or disable the falling trigger */
        if ((GPIO_Init->Mode & FALLING_EDGE) == FALLING_EDGE)
 8000834:	683b      	ldr	r3, [r7, #0]
 8000836:	685b      	ldr	r3, [r3, #4]
 8000838:	f403 1300 	and.w	r3, r3, #2097152	@ 0x200000
 800083c:	2b00      	cmp	r3, #0
 800083e:	d006      	beq.n	800084e <HAL_GPIO_Init+0x24e>
        {
          SET_BIT(EXTI->FTSR, iocurrent);
 8000840:	4b23      	ldr	r3, [pc, #140]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 8000842:	68da      	ldr	r2, [r3, #12]
 8000844:	4922      	ldr	r1, [pc, #136]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 8000846:	69bb      	ldr	r3, [r7, #24]
 8000848:	4313      	orrs	r3, r2
 800084a:	60cb      	str	r3, [r1, #12]
 800084c:	e006      	b.n	800085c <HAL_GPIO_Init+0x25c>
        }
        else
        {
          CLEAR_BIT(EXTI->FTSR, iocurrent);
 800084e:	4b20      	ldr	r3, [pc, #128]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 8000850:	68da      	ldr	r2, [r3, #12]
 8000852:	69bb      	ldr	r3, [r7, #24]
 8000854:	43db      	mvns	r3, r3
 8000856:	491e      	ldr	r1, [pc, #120]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 8000858:	4013      	ands	r3, r2
 800085a:	60cb      	str	r3, [r1, #12]
        }

        /* Configure the event mask */
        if ((GPIO_Init->Mode & GPIO_MODE_EVT) == GPIO_MODE_EVT)
 800085c:	683b      	ldr	r3, [r7, #0]
 800085e:	685b      	ldr	r3, [r3, #4]
 8000860:	f403 3300 	and.w	r3, r3, #131072	@ 0x20000
 8000864:	2b00      	cmp	r3, #0
 8000866:	d006      	beq.n	8000876 <HAL_GPIO_Init+0x276>
        {
          SET_BIT(EXTI->EMR, iocurrent);
 8000868:	4b19      	ldr	r3, [pc, #100]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 800086a:	685a      	ldr	r2, [r3, #4]
 800086c:	4918      	ldr	r1, [pc, #96]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 800086e:	69bb      	ldr	r3, [r7, #24]
 8000870:	4313      	orrs	r3, r2
 8000872:	604b      	str	r3, [r1, #4]
 8000874:	e006      	b.n	8000884 <HAL_GPIO_Init+0x284>
        }
        else
        {
          CLEAR_BIT(EXTI->EMR, iocurrent);
 8000876:	4b16      	ldr	r3, [pc, #88]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 8000878:	685a      	ldr	r2, [r3, #4]
 800087a:	69bb      	ldr	r3, [r7, #24]
 800087c:	43db      	mvns	r3, r3
 800087e:	4914      	ldr	r1, [pc, #80]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 8000880:	4013      	ands	r3, r2
 8000882:	604b      	str	r3, [r1, #4]
        }

        /* Configure the interrupt mask */
        if ((GPIO_Init->Mode & GPIO_MODE_IT) == GPIO_MODE_IT)
 8000884:	683b      	ldr	r3, [r7, #0]
 8000886:	685b      	ldr	r3, [r3, #4]
 8000888:	f403 3380 	and.w	r3, r3, #65536	@ 0x10000
 800088c:	2b00      	cmp	r3, #0
 800088e:	d021      	beq.n	80008d4 <HAL_GPIO_Init+0x2d4>
        {
          SET_BIT(EXTI->IMR, iocurrent);
 8000890:	4b0f      	ldr	r3, [pc, #60]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 8000892:	681a      	ldr	r2, [r3, #0]
 8000894:	490e      	ldr	r1, [pc, #56]	@ (80008d0 <HAL_GPIO_Init+0x2d0>)
 8000896:	69bb      	ldr	r3, [r7, #24]
 8000898:	4313      	orrs	r3, r2
 800089a:	600b      	str	r3, [r1, #0]
 800089c:	e021      	b.n	80008e2 <HAL_GPIO_Init+0x2e2>
 800089e:	bf00      	nop
 80008a0:	10320000 	.word	0x10320000
 80008a4:	10310000 	.word	0x10310000
 80008a8:	10220000 	.word	0x10220000
 80008ac:	10210000 	.word	0x10210000
 80008b0:	10120000 	.word	0x10120000
 80008b4:	10110000 	.word	0x10110000
 80008b8:	40021000 	.word	0x40021000
 80008bc:	40010000 	.word	0x40010000
 80008c0:	40010800 	.word	0x40010800
 80008c4:	40010c00 	.word	0x40010c00
 80008c8:	40011000 	.word	0x40011000
 80008cc:	40011400 	.word	0x40011400
 80008d0:	40010400 	.word	0x40010400
        }
        else
        {
          CLEAR_BIT(EXTI->IMR, iocurrent);
 80008d4:	4b0b      	ldr	r3, [pc, #44]	@ (8000904 <HAL_GPIO_Init+0x304>)
 80008d6:	681a      	ldr	r2, [r3, #0]
 80008d8:	69bb      	ldr	r3, [r7, #24]
 80008da:	43db      	mvns	r3, r3
 80008dc:	4909      	ldr	r1, [pc, #36]	@ (8000904 <HAL_GPIO_Init+0x304>)
 80008de:	4013      	ands	r3, r2
 80008e0:	600b      	str	r3, [r1, #0]
        }
      }
    }

	position++;
 80008e2:	6a7b      	ldr	r3, [r7, #36]	@ 0x24
 80008e4:	3301      	adds	r3, #1
 80008e6:	627b      	str	r3, [r7, #36]	@ 0x24
  while (((GPIO_Init->Pin) >> position) != 0x00u)
 80008e8:	683b      	ldr	r3, [r7, #0]
 80008ea:	681a      	ldr	r2, [r3, #0]
 80008ec:	6a7b      	ldr	r3, [r7, #36]	@ 0x24
 80008ee:	fa22 f303 	lsr.w	r3, r2, r3
 80008f2:	2b00      	cmp	r3, #0
 80008f4:	f47f ae8e 	bne.w	8000614 <HAL_GPIO_Init+0x14>
  }
}
 80008f8:	bf00      	nop
 80008fa:	bf00      	nop
 80008fc:	372c      	adds	r7, #44	@ 0x2c
 80008fe:	46bd      	mov	sp, r7
 8000900:	bc80      	pop	{r7}
 8000902:	4770      	bx	lr
 8000904:	40010400 	.word	0x40010400

08000908 <HAL_GPIO_WritePin>:
  *            @arg GPIO_PIN_RESET: to clear the port pin
  *            @arg GPIO_PIN_SET: to set the port pin
  * @retval None
  */
void HAL_GPIO_WritePin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin, GPIO_PinState PinState)
{
 8000908:	b480      	push	{r7}
 800090a:	b083      	sub	sp, #12
 800090c:	af00      	add	r7, sp, #0
 800090e:	6078      	str	r0, [r7, #4]
 8000910:	460b      	mov	r3, r1
 8000912:	807b      	strh	r3, [r7, #2]
 8000914:	4613      	mov	r3, r2
 8000916:	707b      	strb	r3, [r7, #1]
  /* Check the parameters */
  assert_param(IS_GPIO_PIN(GPIO_Pin));
  assert_param(IS_GPIO_PIN_ACTION(PinState));

  if (PinState != GPIO_PIN_RESET)
 8000918:	787b      	ldrb	r3, [r7, #1]
 800091a:	2b00      	cmp	r3, #0
 800091c:	d003      	beq.n	8000926 <HAL_GPIO_WritePin+0x1e>
  {
    GPIOx->BSRR = GPIO_Pin;
 800091e:	887a      	ldrh	r2, [r7, #2]
 8000920:	687b      	ldr	r3, [r7, #4]
 8000922:	611a      	str	r2, [r3, #16]
  }
  else
  {
    GPIOx->BSRR = (uint32_t)GPIO_Pin << 16u;
  }
}
 8000924:	e003      	b.n	800092e <HAL_GPIO_WritePin+0x26>
    GPIOx->BSRR = (uint32_t)GPIO_Pin << 16u;
 8000926:	887b      	ldrh	r3, [r7, #2]
 8000928:	041a      	lsls	r2, r3, #16
 800092a:	687b      	ldr	r3, [r7, #4]
 800092c:	611a      	str	r2, [r3, #16]
}
 800092e:	bf00      	nop
 8000930:	370c      	adds	r7, #12
 8000932:	46bd      	mov	sp, r7
 8000934:	bc80      	pop	{r7}
 8000936:	4770      	bx	lr

08000938 <HAL_RCC_OscConfig>:
  *         supported by this macro. User should request a transition to HSE Off
  *         first and then HSE On or HSE Bypass.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_RCC_OscConfig(RCC_OscInitTypeDef  *RCC_OscInitStruct)
{
 8000938:	b580      	push	{r7, lr}
 800093a:	b086      	sub	sp, #24
 800093c:	af00      	add	r7, sp, #0
 800093e:	6078      	str	r0, [r7, #4]
  uint32_t tickstart;
  uint32_t pll_config;

  /* Check Null pointer */
  if (RCC_OscInitStruct == NULL)
 8000940:	687b      	ldr	r3, [r7, #4]
 8000942:	2b00      	cmp	r3, #0
 8000944:	d101      	bne.n	800094a <HAL_RCC_OscConfig+0x12>
  {
    return HAL_ERROR;
 8000946:	2301      	movs	r3, #1
 8000948:	e272      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>

  /* Check the parameters */
  assert_param(IS_RCC_OSCILLATORTYPE(RCC_OscInitStruct->OscillatorType));

  /*------------------------------- HSE Configuration ------------------------*/
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSE) == RCC_OSCILLATORTYPE_HSE)
 800094a:	687b      	ldr	r3, [r7, #4]
 800094c:	681b      	ldr	r3, [r3, #0]
 800094e:	f003 0301 	and.w	r3, r3, #1
 8000952:	2b00      	cmp	r3, #0
 8000954:	f000 8087 	beq.w	8000a66 <HAL_RCC_OscConfig+0x12e>
  {
    /* Check the parameters */
    assert_param(IS_RCC_HSE(RCC_OscInitStruct->HSEState));

    /* When the HSE is used as system clock or clock source for PLL in these cases it is not allowed to be disabled */
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_HSE)
 8000958:	4b92      	ldr	r3, [pc, #584]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 800095a:	685b      	ldr	r3, [r3, #4]
 800095c:	f003 030c 	and.w	r3, r3, #12
 8000960:	2b04      	cmp	r3, #4
 8000962:	d00c      	beq.n	800097e <HAL_RCC_OscConfig+0x46>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSE)))
 8000964:	4b8f      	ldr	r3, [pc, #572]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000966:	685b      	ldr	r3, [r3, #4]
 8000968:	f003 030c 	and.w	r3, r3, #12
 800096c:	2b08      	cmp	r3, #8
 800096e:	d112      	bne.n	8000996 <HAL_RCC_OscConfig+0x5e>
 8000970:	4b8c      	ldr	r3, [pc, #560]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000972:	685b      	ldr	r3, [r3, #4]
 8000974:	f403 3380 	and.w	r3, r3, #65536	@ 0x10000
 8000978:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 800097c:	d10b      	bne.n	8000996 <HAL_RCC_OscConfig+0x5e>
    {
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET) && (RCC_OscInitStruct->HSEState == RCC_HSE_OFF))
 800097e:	4b89      	ldr	r3, [pc, #548]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000980:	681b      	ldr	r3, [r3, #0]
 8000982:	f403 3300 	and.w	r3, r3, #131072	@ 0x20000
 8000986:	2b00      	cmp	r3, #0
 8000988:	d06c      	beq.n	8000a64 <HAL_RCC_OscConfig+0x12c>
 800098a:	687b      	ldr	r3, [r7, #4]
 800098c:	685b      	ldr	r3, [r3, #4]
 800098e:	2b00      	cmp	r3, #0
 8000990:	d168      	bne.n	8000a64 <HAL_RCC_OscConfig+0x12c>
      {
        return HAL_ERROR;
 8000992:	2301      	movs	r3, #1
 8000994:	e24c      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
      }
    }
    else
    {
      /* Set the new HSE configuration ---------------------------------------*/
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 8000996:	687b      	ldr	r3, [r7, #4]
 8000998:	685b      	ldr	r3, [r3, #4]
 800099a:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 800099e:	d106      	bne.n	80009ae <HAL_RCC_OscConfig+0x76>
 80009a0:	4b80      	ldr	r3, [pc, #512]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009a2:	681b      	ldr	r3, [r3, #0]
 80009a4:	4a7f      	ldr	r2, [pc, #508]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009a6:	f443 3380 	orr.w	r3, r3, #65536	@ 0x10000
 80009aa:	6013      	str	r3, [r2, #0]
 80009ac:	e02e      	b.n	8000a0c <HAL_RCC_OscConfig+0xd4>
 80009ae:	687b      	ldr	r3, [r7, #4]
 80009b0:	685b      	ldr	r3, [r3, #4]
 80009b2:	2b00      	cmp	r3, #0
 80009b4:	d10c      	bne.n	80009d0 <HAL_RCC_OscConfig+0x98>
 80009b6:	4b7b      	ldr	r3, [pc, #492]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009b8:	681b      	ldr	r3, [r3, #0]
 80009ba:	4a7a      	ldr	r2, [pc, #488]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009bc:	f423 3380 	bic.w	r3, r3, #65536	@ 0x10000
 80009c0:	6013      	str	r3, [r2, #0]
 80009c2:	4b78      	ldr	r3, [pc, #480]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009c4:	681b      	ldr	r3, [r3, #0]
 80009c6:	4a77      	ldr	r2, [pc, #476]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009c8:	f423 2380 	bic.w	r3, r3, #262144	@ 0x40000
 80009cc:	6013      	str	r3, [r2, #0]
 80009ce:	e01d      	b.n	8000a0c <HAL_RCC_OscConfig+0xd4>
 80009d0:	687b      	ldr	r3, [r7, #4]
 80009d2:	685b      	ldr	r3, [r3, #4]
 80009d4:	f5b3 2fa0 	cmp.w	r3, #327680	@ 0x50000
 80009d8:	d10c      	bne.n	80009f4 <HAL_RCC_OscConfig+0xbc>
 80009da:	4b72      	ldr	r3, [pc, #456]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009dc:	681b      	ldr	r3, [r3, #0]
 80009de:	4a71      	ldr	r2, [pc, #452]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009e0:	f443 2380 	orr.w	r3, r3, #262144	@ 0x40000
 80009e4:	6013      	str	r3, [r2, #0]
 80009e6:	4b6f      	ldr	r3, [pc, #444]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009e8:	681b      	ldr	r3, [r3, #0]
 80009ea:	4a6e      	ldr	r2, [pc, #440]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009ec:	f443 3380 	orr.w	r3, r3, #65536	@ 0x10000
 80009f0:	6013      	str	r3, [r2, #0]
 80009f2:	e00b      	b.n	8000a0c <HAL_RCC_OscConfig+0xd4>
 80009f4:	4b6b      	ldr	r3, [pc, #428]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009f6:	681b      	ldr	r3, [r3, #0]
 80009f8:	4a6a      	ldr	r2, [pc, #424]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 80009fa:	f423 3380 	bic.w	r3, r3, #65536	@ 0x10000
 80009fe:	6013      	str	r3, [r2, #0]
 8000a00:	4b68      	ldr	r3, [pc, #416]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000a02:	681b      	ldr	r3, [r3, #0]
 8000a04:	4a67      	ldr	r2, [pc, #412]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000a06:	f423 2380 	bic.w	r3, r3, #262144	@ 0x40000
 8000a0a:	6013      	str	r3, [r2, #0]


      /* Check the HSE State */
      if (RCC_OscInitStruct->HSEState != RCC_HSE_OFF)
 8000a0c:	687b      	ldr	r3, [r7, #4]
 8000a0e:	685b      	ldr	r3, [r3, #4]
 8000a10:	2b00      	cmp	r3, #0
 8000a12:	d013      	beq.n	8000a3c <HAL_RCC_OscConfig+0x104>
      {
        /* Get Start Tick */
        tickstart = HAL_GetTick();
 8000a14:	f7ff fd06 	bl	8000424 <HAL_GetTick>
 8000a18:	6138      	str	r0, [r7, #16]

        /* Wait till HSE is ready */
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 8000a1a:	e008      	b.n	8000a2e <HAL_RCC_OscConfig+0xf6>
        {
          if ((HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 8000a1c:	f7ff fd02 	bl	8000424 <HAL_GetTick>
 8000a20:	4602      	mov	r2, r0
 8000a22:	693b      	ldr	r3, [r7, #16]
 8000a24:	1ad3      	subs	r3, r2, r3
 8000a26:	2b64      	cmp	r3, #100	@ 0x64
 8000a28:	d901      	bls.n	8000a2e <HAL_RCC_OscConfig+0xf6>
          {
            return HAL_TIMEOUT;
 8000a2a:	2303      	movs	r3, #3
 8000a2c:	e200      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 8000a2e:	4b5d      	ldr	r3, [pc, #372]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000a30:	681b      	ldr	r3, [r3, #0]
 8000a32:	f403 3300 	and.w	r3, r3, #131072	@ 0x20000
 8000a36:	2b00      	cmp	r3, #0
 8000a38:	d0f0      	beq.n	8000a1c <HAL_RCC_OscConfig+0xe4>
 8000a3a:	e014      	b.n	8000a66 <HAL_RCC_OscConfig+0x12e>
        }
      }
      else
      {
        /* Get Start Tick */
        tickstart = HAL_GetTick();
 8000a3c:	f7ff fcf2 	bl	8000424 <HAL_GetTick>
 8000a40:	6138      	str	r0, [r7, #16]

        /* Wait till HSE is disabled */
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET)
 8000a42:	e008      	b.n	8000a56 <HAL_RCC_OscConfig+0x11e>
        {
          if ((HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 8000a44:	f7ff fcee 	bl	8000424 <HAL_GetTick>
 8000a48:	4602      	mov	r2, r0
 8000a4a:	693b      	ldr	r3, [r7, #16]
 8000a4c:	1ad3      	subs	r3, r2, r3
 8000a4e:	2b64      	cmp	r3, #100	@ 0x64
 8000a50:	d901      	bls.n	8000a56 <HAL_RCC_OscConfig+0x11e>
          {
            return HAL_TIMEOUT;
 8000a52:	2303      	movs	r3, #3
 8000a54:	e1ec      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET)
 8000a56:	4b53      	ldr	r3, [pc, #332]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000a58:	681b      	ldr	r3, [r3, #0]
 8000a5a:	f403 3300 	and.w	r3, r3, #131072	@ 0x20000
 8000a5e:	2b00      	cmp	r3, #0
 8000a60:	d1f0      	bne.n	8000a44 <HAL_RCC_OscConfig+0x10c>
 8000a62:	e000      	b.n	8000a66 <HAL_RCC_OscConfig+0x12e>
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET) && (RCC_OscInitStruct->HSEState == RCC_HSE_OFF))
 8000a64:	bf00      	nop
        }
      }
    }
  }
  /*----------------------------- HSI Configuration --------------------------*/
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSI) == RCC_OSCILLATORTYPE_HSI)
 8000a66:	687b      	ldr	r3, [r7, #4]
 8000a68:	681b      	ldr	r3, [r3, #0]
 8000a6a:	f003 0302 	and.w	r3, r3, #2
 8000a6e:	2b00      	cmp	r3, #0
 8000a70:	d063      	beq.n	8000b3a <HAL_RCC_OscConfig+0x202>
    /* Check the parameters */
    assert_param(IS_RCC_HSI(RCC_OscInitStruct->HSIState));
    assert_param(IS_RCC_CALIBRATION_VALUE(RCC_OscInitStruct->HSICalibrationValue));

    /* Check if HSI is used as system clock or as PLL source when PLL is selected as system clock */
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_HSI)
 8000a72:	4b4c      	ldr	r3, [pc, #304]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000a74:	685b      	ldr	r3, [r3, #4]
 8000a76:	f003 030c 	and.w	r3, r3, #12
 8000a7a:	2b00      	cmp	r3, #0
 8000a7c:	d00b      	beq.n	8000a96 <HAL_RCC_OscConfig+0x15e>
        || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSI_DIV2)))
 8000a7e:	4b49      	ldr	r3, [pc, #292]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000a80:	685b      	ldr	r3, [r3, #4]
 8000a82:	f003 030c 	and.w	r3, r3, #12
 8000a86:	2b08      	cmp	r3, #8
 8000a88:	d11c      	bne.n	8000ac4 <HAL_RCC_OscConfig+0x18c>
 8000a8a:	4b46      	ldr	r3, [pc, #280]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000a8c:	685b      	ldr	r3, [r3, #4]
 8000a8e:	f403 3380 	and.w	r3, r3, #65536	@ 0x10000
 8000a92:	2b00      	cmp	r3, #0
 8000a94:	d116      	bne.n	8000ac4 <HAL_RCC_OscConfig+0x18c>
    {
      /* When HSI is used as system clock it will not disabled */
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET) && (RCC_OscInitStruct->HSIState != RCC_HSI_ON))
 8000a96:	4b43      	ldr	r3, [pc, #268]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000a98:	681b      	ldr	r3, [r3, #0]
 8000a9a:	f003 0302 	and.w	r3, r3, #2
 8000a9e:	2b00      	cmp	r3, #0
 8000aa0:	d005      	beq.n	8000aae <HAL_RCC_OscConfig+0x176>
 8000aa2:	687b      	ldr	r3, [r7, #4]
 8000aa4:	691b      	ldr	r3, [r3, #16]
 8000aa6:	2b01      	cmp	r3, #1
 8000aa8:	d001      	beq.n	8000aae <HAL_RCC_OscConfig+0x176>
      {
        return HAL_ERROR;
 8000aaa:	2301      	movs	r3, #1
 8000aac:	e1c0      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
      }
      /* Otherwise, just the calibration is allowed */
      else
      {
        /* Adjusts the Internal High Speed oscillator (HSI) calibration value.*/
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 8000aae:	4b3d      	ldr	r3, [pc, #244]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000ab0:	681b      	ldr	r3, [r3, #0]
 8000ab2:	f023 02f8 	bic.w	r2, r3, #248	@ 0xf8
 8000ab6:	687b      	ldr	r3, [r7, #4]
 8000ab8:	695b      	ldr	r3, [r3, #20]
 8000aba:	00db      	lsls	r3, r3, #3
 8000abc:	4939      	ldr	r1, [pc, #228]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000abe:	4313      	orrs	r3, r2
 8000ac0:	600b      	str	r3, [r1, #0]
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET) && (RCC_OscInitStruct->HSIState != RCC_HSI_ON))
 8000ac2:	e03a      	b.n	8000b3a <HAL_RCC_OscConfig+0x202>
      }
    }
    else
    {
      /* Check the HSI State */
      if (RCC_OscInitStruct->HSIState != RCC_HSI_OFF)
 8000ac4:	687b      	ldr	r3, [r7, #4]
 8000ac6:	691b      	ldr	r3, [r3, #16]
 8000ac8:	2b00      	cmp	r3, #0
 8000aca:	d020      	beq.n	8000b0e <HAL_RCC_OscConfig+0x1d6>
      {
        /* Enable the Internal High Speed oscillator (HSI). */
        __HAL_RCC_HSI_ENABLE();
 8000acc:	4b36      	ldr	r3, [pc, #216]	@ (8000ba8 <HAL_RCC_OscConfig+0x270>)
 8000ace:	2201      	movs	r2, #1
 8000ad0:	601a      	str	r2, [r3, #0]

        /* Get Start Tick */
        tickstart = HAL_GetTick();
 8000ad2:	f7ff fca7 	bl	8000424 <HAL_GetTick>
 8000ad6:	6138      	str	r0, [r7, #16]

        /* Wait till HSI is ready */
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 8000ad8:	e008      	b.n	8000aec <HAL_RCC_OscConfig+0x1b4>
        {
          if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 8000ada:	f7ff fca3 	bl	8000424 <HAL_GetTick>
 8000ade:	4602      	mov	r2, r0
 8000ae0:	693b      	ldr	r3, [r7, #16]
 8000ae2:	1ad3      	subs	r3, r2, r3
 8000ae4:	2b02      	cmp	r3, #2
 8000ae6:	d901      	bls.n	8000aec <HAL_RCC_OscConfig+0x1b4>
          {
            return HAL_TIMEOUT;
 8000ae8:	2303      	movs	r3, #3
 8000aea:	e1a1      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 8000aec:	4b2d      	ldr	r3, [pc, #180]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000aee:	681b      	ldr	r3, [r3, #0]
 8000af0:	f003 0302 	and.w	r3, r3, #2
 8000af4:	2b00      	cmp	r3, #0
 8000af6:	d0f0      	beq.n	8000ada <HAL_RCC_OscConfig+0x1a2>
          }
        }

        /* Adjusts the Internal High Speed oscillator (HSI) calibration value.*/
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 8000af8:	4b2a      	ldr	r3, [pc, #168]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000afa:	681b      	ldr	r3, [r3, #0]
 8000afc:	f023 02f8 	bic.w	r2, r3, #248	@ 0xf8
 8000b00:	687b      	ldr	r3, [r7, #4]
 8000b02:	695b      	ldr	r3, [r3, #20]
 8000b04:	00db      	lsls	r3, r3, #3
 8000b06:	4927      	ldr	r1, [pc, #156]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000b08:	4313      	orrs	r3, r2
 8000b0a:	600b      	str	r3, [r1, #0]
 8000b0c:	e015      	b.n	8000b3a <HAL_RCC_OscConfig+0x202>
      }
      else
      {
        /* Disable the Internal High Speed oscillator (HSI). */
        __HAL_RCC_HSI_DISABLE();
 8000b0e:	4b26      	ldr	r3, [pc, #152]	@ (8000ba8 <HAL_RCC_OscConfig+0x270>)
 8000b10:	2200      	movs	r2, #0
 8000b12:	601a      	str	r2, [r3, #0]

        /* Get Start Tick */
        tickstart = HAL_GetTick();
 8000b14:	f7ff fc86 	bl	8000424 <HAL_GetTick>
 8000b18:	6138      	str	r0, [r7, #16]

        /* Wait till HSI is disabled */
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET)
 8000b1a:	e008      	b.n	8000b2e <HAL_RCC_OscConfig+0x1f6>
        {
          if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 8000b1c:	f7ff fc82 	bl	8000424 <HAL_GetTick>
 8000b20:	4602      	mov	r2, r0
 8000b22:	693b      	ldr	r3, [r7, #16]
 8000b24:	1ad3      	subs	r3, r2, r3
 8000b26:	2b02      	cmp	r3, #2
 8000b28:	d901      	bls.n	8000b2e <HAL_RCC_OscConfig+0x1f6>
          {
            return HAL_TIMEOUT;
 8000b2a:	2303      	movs	r3, #3
 8000b2c:	e180      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET)
 8000b2e:	4b1d      	ldr	r3, [pc, #116]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000b30:	681b      	ldr	r3, [r3, #0]
 8000b32:	f003 0302 	and.w	r3, r3, #2
 8000b36:	2b00      	cmp	r3, #0
 8000b38:	d1f0      	bne.n	8000b1c <HAL_RCC_OscConfig+0x1e4>
        }
      }
    }
  }
  /*------------------------------ LSI Configuration -------------------------*/
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSI) == RCC_OSCILLATORTYPE_LSI)
 8000b3a:	687b      	ldr	r3, [r7, #4]
 8000b3c:	681b      	ldr	r3, [r3, #0]
 8000b3e:	f003 0308 	and.w	r3, r3, #8
 8000b42:	2b00      	cmp	r3, #0
 8000b44:	d03a      	beq.n	8000bbc <HAL_RCC_OscConfig+0x284>
  {
    /* Check the parameters */
    assert_param(IS_RCC_LSI(RCC_OscInitStruct->LSIState));

    /* Check the LSI State */
    if (RCC_OscInitStruct->LSIState != RCC_LSI_OFF)
 8000b46:	687b      	ldr	r3, [r7, #4]
 8000b48:	699b      	ldr	r3, [r3, #24]
 8000b4a:	2b00      	cmp	r3, #0
 8000b4c:	d019      	beq.n	8000b82 <HAL_RCC_OscConfig+0x24a>
    {
      /* Enable the Internal Low Speed oscillator (LSI). */
      __HAL_RCC_LSI_ENABLE();
 8000b4e:	4b17      	ldr	r3, [pc, #92]	@ (8000bac <HAL_RCC_OscConfig+0x274>)
 8000b50:	2201      	movs	r2, #1
 8000b52:	601a      	str	r2, [r3, #0]

      /* Get Start Tick */
      tickstart = HAL_GetTick();
 8000b54:	f7ff fc66 	bl	8000424 <HAL_GetTick>
 8000b58:	6138      	str	r0, [r7, #16]

      /* Wait till LSI is ready */
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) == RESET)
 8000b5a:	e008      	b.n	8000b6e <HAL_RCC_OscConfig+0x236>
      {
        if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 8000b5c:	f7ff fc62 	bl	8000424 <HAL_GetTick>
 8000b60:	4602      	mov	r2, r0
 8000b62:	693b      	ldr	r3, [r7, #16]
 8000b64:	1ad3      	subs	r3, r2, r3
 8000b66:	2b02      	cmp	r3, #2
 8000b68:	d901      	bls.n	8000b6e <HAL_RCC_OscConfig+0x236>
        {
          return HAL_TIMEOUT;
 8000b6a:	2303      	movs	r3, #3
 8000b6c:	e160      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) == RESET)
 8000b6e:	4b0d      	ldr	r3, [pc, #52]	@ (8000ba4 <HAL_RCC_OscConfig+0x26c>)
 8000b70:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8000b72:	f003 0302 	and.w	r3, r3, #2
 8000b76:	2b00      	cmp	r3, #0
 8000b78:	d0f0      	beq.n	8000b5c <HAL_RCC_OscConfig+0x224>
        }
      }
      /*  To have a fully stabilized clock in the specified range, a software delay of 1ms
          should be added.*/
      RCC_Delay(1);
 8000b7a:	2001      	movs	r0, #1
 8000b7c:	f000 fa9c 	bl	80010b8 <RCC_Delay>
 8000b80:	e01c      	b.n	8000bbc <HAL_RCC_OscConfig+0x284>
    }
    else
    {
      /* Disable the Internal Low Speed oscillator (LSI). */
      __HAL_RCC_LSI_DISABLE();
 8000b82:	4b0a      	ldr	r3, [pc, #40]	@ (8000bac <HAL_RCC_OscConfig+0x274>)
 8000b84:	2200      	movs	r2, #0
 8000b86:	601a      	str	r2, [r3, #0]

      /* Get Start Tick */
      tickstart = HAL_GetTick();
 8000b88:	f7ff fc4c 	bl	8000424 <HAL_GetTick>
 8000b8c:	6138      	str	r0, [r7, #16]

      /* Wait till LSI is disabled */
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) != RESET)
 8000b8e:	e00f      	b.n	8000bb0 <HAL_RCC_OscConfig+0x278>
      {
        if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 8000b90:	f7ff fc48 	bl	8000424 <HAL_GetTick>
 8000b94:	4602      	mov	r2, r0
 8000b96:	693b      	ldr	r3, [r7, #16]
 8000b98:	1ad3      	subs	r3, r2, r3
 8000b9a:	2b02      	cmp	r3, #2
 8000b9c:	d908      	bls.n	8000bb0 <HAL_RCC_OscConfig+0x278>
        {
          return HAL_TIMEOUT;
 8000b9e:	2303      	movs	r3, #3
 8000ba0:	e146      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
 8000ba2:	bf00      	nop
 8000ba4:	40021000 	.word	0x40021000
 8000ba8:	42420000 	.word	0x42420000
 8000bac:	42420480 	.word	0x42420480
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) != RESET)
 8000bb0:	4b92      	ldr	r3, [pc, #584]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000bb2:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8000bb4:	f003 0302 	and.w	r3, r3, #2
 8000bb8:	2b00      	cmp	r3, #0
 8000bba:	d1e9      	bne.n	8000b90 <HAL_RCC_OscConfig+0x258>
        }
      }
    }
  }
  /*------------------------------ LSE Configuration -------------------------*/
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSE) == RCC_OSCILLATORTYPE_LSE)
 8000bbc:	687b      	ldr	r3, [r7, #4]
 8000bbe:	681b      	ldr	r3, [r3, #0]
 8000bc0:	f003 0304 	and.w	r3, r3, #4
 8000bc4:	2b00      	cmp	r3, #0
 8000bc6:	f000 80a6 	beq.w	8000d16 <HAL_RCC_OscConfig+0x3de>
  {
    FlagStatus       pwrclkchanged = RESET;
 8000bca:	2300      	movs	r3, #0
 8000bcc:	75fb      	strb	r3, [r7, #23]
    /* Check the parameters */
    assert_param(IS_RCC_LSE(RCC_OscInitStruct->LSEState));

    /* Update LSE configuration in Backup Domain control register    */
    /* Requires to enable write access to Backup Domain of necessary */
    if (__HAL_RCC_PWR_IS_CLK_DISABLED())
 8000bce:	4b8b      	ldr	r3, [pc, #556]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000bd0:	69db      	ldr	r3, [r3, #28]
 8000bd2:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 8000bd6:	2b00      	cmp	r3, #0
 8000bd8:	d10d      	bne.n	8000bf6 <HAL_RCC_OscConfig+0x2be>
    {
      __HAL_RCC_PWR_CLK_ENABLE();
 8000bda:	4b88      	ldr	r3, [pc, #544]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000bdc:	69db      	ldr	r3, [r3, #28]
 8000bde:	4a87      	ldr	r2, [pc, #540]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000be0:	f043 5380 	orr.w	r3, r3, #268435456	@ 0x10000000
 8000be4:	61d3      	str	r3, [r2, #28]
 8000be6:	4b85      	ldr	r3, [pc, #532]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000be8:	69db      	ldr	r3, [r3, #28]
 8000bea:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 8000bee:	60bb      	str	r3, [r7, #8]
 8000bf0:	68bb      	ldr	r3, [r7, #8]
      pwrclkchanged = SET;
 8000bf2:	2301      	movs	r3, #1
 8000bf4:	75fb      	strb	r3, [r7, #23]
    }

    if (HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8000bf6:	4b82      	ldr	r3, [pc, #520]	@ (8000e00 <HAL_RCC_OscConfig+0x4c8>)
 8000bf8:	681b      	ldr	r3, [r3, #0]
 8000bfa:	f403 7380 	and.w	r3, r3, #256	@ 0x100
 8000bfe:	2b00      	cmp	r3, #0
 8000c00:	d118      	bne.n	8000c34 <HAL_RCC_OscConfig+0x2fc>
    {
      /* Enable write access to Backup domain */
      SET_BIT(PWR->CR, PWR_CR_DBP);
 8000c02:	4b7f      	ldr	r3, [pc, #508]	@ (8000e00 <HAL_RCC_OscConfig+0x4c8>)
 8000c04:	681b      	ldr	r3, [r3, #0]
 8000c06:	4a7e      	ldr	r2, [pc, #504]	@ (8000e00 <HAL_RCC_OscConfig+0x4c8>)
 8000c08:	f443 7380 	orr.w	r3, r3, #256	@ 0x100
 8000c0c:	6013      	str	r3, [r2, #0]

      /* Wait for Backup domain Write protection disable */
      tickstart = HAL_GetTick();
 8000c0e:	f7ff fc09 	bl	8000424 <HAL_GetTick>
 8000c12:	6138      	str	r0, [r7, #16]

      while (HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8000c14:	e008      	b.n	8000c28 <HAL_RCC_OscConfig+0x2f0>
      {
        if ((HAL_GetTick() - tickstart) > RCC_DBP_TIMEOUT_VALUE)
 8000c16:	f7ff fc05 	bl	8000424 <HAL_GetTick>
 8000c1a:	4602      	mov	r2, r0
 8000c1c:	693b      	ldr	r3, [r7, #16]
 8000c1e:	1ad3      	subs	r3, r2, r3
 8000c20:	2b64      	cmp	r3, #100	@ 0x64
 8000c22:	d901      	bls.n	8000c28 <HAL_RCC_OscConfig+0x2f0>
        {
          return HAL_TIMEOUT;
 8000c24:	2303      	movs	r3, #3
 8000c26:	e103      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
      while (HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8000c28:	4b75      	ldr	r3, [pc, #468]	@ (8000e00 <HAL_RCC_OscConfig+0x4c8>)
 8000c2a:	681b      	ldr	r3, [r3, #0]
 8000c2c:	f403 7380 	and.w	r3, r3, #256	@ 0x100
 8000c30:	2b00      	cmp	r3, #0
 8000c32:	d0f0      	beq.n	8000c16 <HAL_RCC_OscConfig+0x2de>
        }
      }
    }

    /* Set the new LSE configuration -----------------------------------------*/
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 8000c34:	687b      	ldr	r3, [r7, #4]
 8000c36:	68db      	ldr	r3, [r3, #12]
 8000c38:	2b01      	cmp	r3, #1
 8000c3a:	d106      	bne.n	8000c4a <HAL_RCC_OscConfig+0x312>
 8000c3c:	4b6f      	ldr	r3, [pc, #444]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c3e:	6a1b      	ldr	r3, [r3, #32]
 8000c40:	4a6e      	ldr	r2, [pc, #440]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c42:	f043 0301 	orr.w	r3, r3, #1
 8000c46:	6213      	str	r3, [r2, #32]
 8000c48:	e02d      	b.n	8000ca6 <HAL_RCC_OscConfig+0x36e>
 8000c4a:	687b      	ldr	r3, [r7, #4]
 8000c4c:	68db      	ldr	r3, [r3, #12]
 8000c4e:	2b00      	cmp	r3, #0
 8000c50:	d10c      	bne.n	8000c6c <HAL_RCC_OscConfig+0x334>
 8000c52:	4b6a      	ldr	r3, [pc, #424]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c54:	6a1b      	ldr	r3, [r3, #32]
 8000c56:	4a69      	ldr	r2, [pc, #420]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c58:	f023 0301 	bic.w	r3, r3, #1
 8000c5c:	6213      	str	r3, [r2, #32]
 8000c5e:	4b67      	ldr	r3, [pc, #412]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c60:	6a1b      	ldr	r3, [r3, #32]
 8000c62:	4a66      	ldr	r2, [pc, #408]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c64:	f023 0304 	bic.w	r3, r3, #4
 8000c68:	6213      	str	r3, [r2, #32]
 8000c6a:	e01c      	b.n	8000ca6 <HAL_RCC_OscConfig+0x36e>
 8000c6c:	687b      	ldr	r3, [r7, #4]
 8000c6e:	68db      	ldr	r3, [r3, #12]
 8000c70:	2b05      	cmp	r3, #5
 8000c72:	d10c      	bne.n	8000c8e <HAL_RCC_OscConfig+0x356>
 8000c74:	4b61      	ldr	r3, [pc, #388]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c76:	6a1b      	ldr	r3, [r3, #32]
 8000c78:	4a60      	ldr	r2, [pc, #384]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c7a:	f043 0304 	orr.w	r3, r3, #4
 8000c7e:	6213      	str	r3, [r2, #32]
 8000c80:	4b5e      	ldr	r3, [pc, #376]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c82:	6a1b      	ldr	r3, [r3, #32]
 8000c84:	4a5d      	ldr	r2, [pc, #372]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c86:	f043 0301 	orr.w	r3, r3, #1
 8000c8a:	6213      	str	r3, [r2, #32]
 8000c8c:	e00b      	b.n	8000ca6 <HAL_RCC_OscConfig+0x36e>
 8000c8e:	4b5b      	ldr	r3, [pc, #364]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c90:	6a1b      	ldr	r3, [r3, #32]
 8000c92:	4a5a      	ldr	r2, [pc, #360]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c94:	f023 0301 	bic.w	r3, r3, #1
 8000c98:	6213      	str	r3, [r2, #32]
 8000c9a:	4b58      	ldr	r3, [pc, #352]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000c9c:	6a1b      	ldr	r3, [r3, #32]
 8000c9e:	4a57      	ldr	r2, [pc, #348]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000ca0:	f023 0304 	bic.w	r3, r3, #4
 8000ca4:	6213      	str	r3, [r2, #32]
    /* Check the LSE State */
    if (RCC_OscInitStruct->LSEState != RCC_LSE_OFF)
 8000ca6:	687b      	ldr	r3, [r7, #4]
 8000ca8:	68db      	ldr	r3, [r3, #12]
 8000caa:	2b00      	cmp	r3, #0
 8000cac:	d015      	beq.n	8000cda <HAL_RCC_OscConfig+0x3a2>
    {
      /* Get Start Tick */
      tickstart = HAL_GetTick();
 8000cae:	f7ff fbb9 	bl	8000424 <HAL_GetTick>
 8000cb2:	6138      	str	r0, [r7, #16]

      /* Wait till LSE is ready */
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 8000cb4:	e00a      	b.n	8000ccc <HAL_RCC_OscConfig+0x394>
      {
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 8000cb6:	f7ff fbb5 	bl	8000424 <HAL_GetTick>
 8000cba:	4602      	mov	r2, r0
 8000cbc:	693b      	ldr	r3, [r7, #16]
 8000cbe:	1ad3      	subs	r3, r2, r3
 8000cc0:	f241 3288 	movw	r2, #5000	@ 0x1388
 8000cc4:	4293      	cmp	r3, r2
 8000cc6:	d901      	bls.n	8000ccc <HAL_RCC_OscConfig+0x394>
        {
          return HAL_TIMEOUT;
 8000cc8:	2303      	movs	r3, #3
 8000cca:	e0b1      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 8000ccc:	4b4b      	ldr	r3, [pc, #300]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000cce:	6a1b      	ldr	r3, [r3, #32]
 8000cd0:	f003 0302 	and.w	r3, r3, #2
 8000cd4:	2b00      	cmp	r3, #0
 8000cd6:	d0ee      	beq.n	8000cb6 <HAL_RCC_OscConfig+0x37e>
 8000cd8:	e014      	b.n	8000d04 <HAL_RCC_OscConfig+0x3cc>
      }
    }
    else
    {
      /* Get Start Tick */
      tickstart = HAL_GetTick();
 8000cda:	f7ff fba3 	bl	8000424 <HAL_GetTick>
 8000cde:	6138      	str	r0, [r7, #16]

      /* Wait till LSE is disabled */
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) != RESET)
 8000ce0:	e00a      	b.n	8000cf8 <HAL_RCC_OscConfig+0x3c0>
      {
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 8000ce2:	f7ff fb9f 	bl	8000424 <HAL_GetTick>
 8000ce6:	4602      	mov	r2, r0
 8000ce8:	693b      	ldr	r3, [r7, #16]
 8000cea:	1ad3      	subs	r3, r2, r3
 8000cec:	f241 3288 	movw	r2, #5000	@ 0x1388
 8000cf0:	4293      	cmp	r3, r2
 8000cf2:	d901      	bls.n	8000cf8 <HAL_RCC_OscConfig+0x3c0>
        {
          return HAL_TIMEOUT;
 8000cf4:	2303      	movs	r3, #3
 8000cf6:	e09b      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) != RESET)
 8000cf8:	4b40      	ldr	r3, [pc, #256]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000cfa:	6a1b      	ldr	r3, [r3, #32]
 8000cfc:	f003 0302 	and.w	r3, r3, #2
 8000d00:	2b00      	cmp	r3, #0
 8000d02:	d1ee      	bne.n	8000ce2 <HAL_RCC_OscConfig+0x3aa>
        }
      }
    }

    /* Require to disable power clock if necessary */
    if (pwrclkchanged == SET)
 8000d04:	7dfb      	ldrb	r3, [r7, #23]
 8000d06:	2b01      	cmp	r3, #1
 8000d08:	d105      	bne.n	8000d16 <HAL_RCC_OscConfig+0x3de>
    {
      __HAL_RCC_PWR_CLK_DISABLE();
 8000d0a:	4b3c      	ldr	r3, [pc, #240]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000d0c:	69db      	ldr	r3, [r3, #28]
 8000d0e:	4a3b      	ldr	r2, [pc, #236]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000d10:	f023 5380 	bic.w	r3, r3, #268435456	@ 0x10000000
 8000d14:	61d3      	str	r3, [r2, #28]

#endif /* RCC_CR_PLL2ON */
  /*-------------------------------- PLL Configuration -----------------------*/
  /* Check the parameters */
  assert_param(IS_RCC_PLL(RCC_OscInitStruct->PLL.PLLState));
  if ((RCC_OscInitStruct->PLL.PLLState) != RCC_PLL_NONE)
 8000d16:	687b      	ldr	r3, [r7, #4]
 8000d18:	69db      	ldr	r3, [r3, #28]
 8000d1a:	2b00      	cmp	r3, #0
 8000d1c:	f000 8087 	beq.w	8000e2e <HAL_RCC_OscConfig+0x4f6>
  {
    /* Check if the PLL is used as system clock or not */
    if (__HAL_RCC_GET_SYSCLK_SOURCE() != RCC_SYSCLKSOURCE_STATUS_PLLCLK)
 8000d20:	4b36      	ldr	r3, [pc, #216]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000d22:	685b      	ldr	r3, [r3, #4]
 8000d24:	f003 030c 	and.w	r3, r3, #12
 8000d28:	2b08      	cmp	r3, #8
 8000d2a:	d061      	beq.n	8000df0 <HAL_RCC_OscConfig+0x4b8>
    {
      if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_ON)
 8000d2c:	687b      	ldr	r3, [r7, #4]
 8000d2e:	69db      	ldr	r3, [r3, #28]
 8000d30:	2b02      	cmp	r3, #2
 8000d32:	d146      	bne.n	8000dc2 <HAL_RCC_OscConfig+0x48a>
        /* Check the parameters */
        assert_param(IS_RCC_PLLSOURCE(RCC_OscInitStruct->PLL.PLLSource));
        assert_param(IS_RCC_PLL_MUL(RCC_OscInitStruct->PLL.PLLMUL));

        /* Disable the main PLL. */
        __HAL_RCC_PLL_DISABLE();
 8000d34:	4b33      	ldr	r3, [pc, #204]	@ (8000e04 <HAL_RCC_OscConfig+0x4cc>)
 8000d36:	2200      	movs	r2, #0
 8000d38:	601a      	str	r2, [r3, #0]

        /* Get Start Tick */
        tickstart = HAL_GetTick();
 8000d3a:	f7ff fb73 	bl	8000424 <HAL_GetTick>
 8000d3e:	6138      	str	r0, [r7, #16]

        /* Wait till PLL is disabled */
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 8000d40:	e008      	b.n	8000d54 <HAL_RCC_OscConfig+0x41c>
        {
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 8000d42:	f7ff fb6f 	bl	8000424 <HAL_GetTick>
 8000d46:	4602      	mov	r2, r0
 8000d48:	693b      	ldr	r3, [r7, #16]
 8000d4a:	1ad3      	subs	r3, r2, r3
 8000d4c:	2b02      	cmp	r3, #2
 8000d4e:	d901      	bls.n	8000d54 <HAL_RCC_OscConfig+0x41c>
          {
            return HAL_TIMEOUT;
 8000d50:	2303      	movs	r3, #3
 8000d52:	e06d      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 8000d54:	4b29      	ldr	r3, [pc, #164]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000d56:	681b      	ldr	r3, [r3, #0]
 8000d58:	f003 7300 	and.w	r3, r3, #33554432	@ 0x2000000
 8000d5c:	2b00      	cmp	r3, #0
 8000d5e:	d1f0      	bne.n	8000d42 <HAL_RCC_OscConfig+0x40a>
          }
        }

        /* Configure the HSE prediv factor --------------------------------*/
        /* It can be written only when the PLL is disabled. Not used in PLL source is different than HSE */
        if (RCC_OscInitStruct->PLL.PLLSource == RCC_PLLSOURCE_HSE)
 8000d60:	687b      	ldr	r3, [r7, #4]
 8000d62:	6a1b      	ldr	r3, [r3, #32]
 8000d64:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 8000d68:	d108      	bne.n	8000d7c <HAL_RCC_OscConfig+0x444>
          /* Set PREDIV1 source */
          SET_BIT(RCC->CFGR2, RCC_OscInitStruct->Prediv1Source);
#endif /* RCC_CFGR2_PREDIV1SRC */

          /* Set PREDIV1 Value */
          __HAL_RCC_HSE_PREDIV_CONFIG(RCC_OscInitStruct->HSEPredivValue);
 8000d6a:	4b24      	ldr	r3, [pc, #144]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000d6c:	685b      	ldr	r3, [r3, #4]
 8000d6e:	f423 3200 	bic.w	r2, r3, #131072	@ 0x20000
 8000d72:	687b      	ldr	r3, [r7, #4]
 8000d74:	689b      	ldr	r3, [r3, #8]
 8000d76:	4921      	ldr	r1, [pc, #132]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000d78:	4313      	orrs	r3, r2
 8000d7a:	604b      	str	r3, [r1, #4]
        }

        /* Configure the main PLL clock source and multiplication factors. */
        __HAL_RCC_PLL_CONFIG(RCC_OscInitStruct->PLL.PLLSource,
 8000d7c:	4b1f      	ldr	r3, [pc, #124]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000d7e:	685b      	ldr	r3, [r3, #4]
 8000d80:	f423 1274 	bic.w	r2, r3, #3997696	@ 0x3d0000
 8000d84:	687b      	ldr	r3, [r7, #4]
 8000d86:	6a19      	ldr	r1, [r3, #32]
 8000d88:	687b      	ldr	r3, [r7, #4]
 8000d8a:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8000d8c:	430b      	orrs	r3, r1
 8000d8e:	491b      	ldr	r1, [pc, #108]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000d90:	4313      	orrs	r3, r2
 8000d92:	604b      	str	r3, [r1, #4]
                             RCC_OscInitStruct->PLL.PLLMUL);
        /* Enable the main PLL. */
        __HAL_RCC_PLL_ENABLE();
 8000d94:	4b1b      	ldr	r3, [pc, #108]	@ (8000e04 <HAL_RCC_OscConfig+0x4cc>)
 8000d96:	2201      	movs	r2, #1
 8000d98:	601a      	str	r2, [r3, #0]

        /* Get Start Tick */
        tickstart = HAL_GetTick();
 8000d9a:	f7ff fb43 	bl	8000424 <HAL_GetTick>
 8000d9e:	6138      	str	r0, [r7, #16]

        /* Wait till PLL is ready */
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  == RESET)
 8000da0:	e008      	b.n	8000db4 <HAL_RCC_OscConfig+0x47c>
        {
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 8000da2:	f7ff fb3f 	bl	8000424 <HAL_GetTick>
 8000da6:	4602      	mov	r2, r0
 8000da8:	693b      	ldr	r3, [r7, #16]
 8000daa:	1ad3      	subs	r3, r2, r3
 8000dac:	2b02      	cmp	r3, #2
 8000dae:	d901      	bls.n	8000db4 <HAL_RCC_OscConfig+0x47c>
          {
            return HAL_TIMEOUT;
 8000db0:	2303      	movs	r3, #3
 8000db2:	e03d      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  == RESET)
 8000db4:	4b11      	ldr	r3, [pc, #68]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000db6:	681b      	ldr	r3, [r3, #0]
 8000db8:	f003 7300 	and.w	r3, r3, #33554432	@ 0x2000000
 8000dbc:	2b00      	cmp	r3, #0
 8000dbe:	d0f0      	beq.n	8000da2 <HAL_RCC_OscConfig+0x46a>
 8000dc0:	e035      	b.n	8000e2e <HAL_RCC_OscConfig+0x4f6>
        }
      }
      else
      {
        /* Disable the main PLL. */
        __HAL_RCC_PLL_DISABLE();
 8000dc2:	4b10      	ldr	r3, [pc, #64]	@ (8000e04 <HAL_RCC_OscConfig+0x4cc>)
 8000dc4:	2200      	movs	r2, #0
 8000dc6:	601a      	str	r2, [r3, #0]

        /* Get Start Tick */
        tickstart = HAL_GetTick();
 8000dc8:	f7ff fb2c 	bl	8000424 <HAL_GetTick>
 8000dcc:	6138      	str	r0, [r7, #16]

        /* Wait till PLL is disabled */
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 8000dce:	e008      	b.n	8000de2 <HAL_RCC_OscConfig+0x4aa>
        {
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 8000dd0:	f7ff fb28 	bl	8000424 <HAL_GetTick>
 8000dd4:	4602      	mov	r2, r0
 8000dd6:	693b      	ldr	r3, [r7, #16]
 8000dd8:	1ad3      	subs	r3, r2, r3
 8000dda:	2b02      	cmp	r3, #2
 8000ddc:	d901      	bls.n	8000de2 <HAL_RCC_OscConfig+0x4aa>
          {
            return HAL_TIMEOUT;
 8000dde:	2303      	movs	r3, #3
 8000de0:	e026      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 8000de2:	4b06      	ldr	r3, [pc, #24]	@ (8000dfc <HAL_RCC_OscConfig+0x4c4>)
 8000de4:	681b      	ldr	r3, [r3, #0]
 8000de6:	f003 7300 	and.w	r3, r3, #33554432	@ 0x2000000
 8000dea:	2b00      	cmp	r3, #0
 8000dec:	d1f0      	bne.n	8000dd0 <HAL_RCC_OscConfig+0x498>
 8000dee:	e01e      	b.n	8000e2e <HAL_RCC_OscConfig+0x4f6>
      }
    }
    else
    {
      /* Check if there is a request to disable the PLL used as System clock source */
      if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_OFF)
 8000df0:	687b      	ldr	r3, [r7, #4]
 8000df2:	69db      	ldr	r3, [r3, #28]
 8000df4:	2b01      	cmp	r3, #1
 8000df6:	d107      	bne.n	8000e08 <HAL_RCC_OscConfig+0x4d0>
      {
        return HAL_ERROR;
 8000df8:	2301      	movs	r3, #1
 8000dfa:	e019      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
 8000dfc:	40021000 	.word	0x40021000
 8000e00:	40007000 	.word	0x40007000
 8000e04:	42420060 	.word	0x42420060
      }
      else
      {
        /* Do not return HAL_ERROR if request repeats the current configuration */
        pll_config = RCC->CFGR;
 8000e08:	4b0b      	ldr	r3, [pc, #44]	@ (8000e38 <HAL_RCC_OscConfig+0x500>)
 8000e0a:	685b      	ldr	r3, [r3, #4]
 8000e0c:	60fb      	str	r3, [r7, #12]
        if ((READ_BIT(pll_config, RCC_CFGR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 8000e0e:	68fb      	ldr	r3, [r7, #12]
 8000e10:	f403 3280 	and.w	r2, r3, #65536	@ 0x10000
 8000e14:	687b      	ldr	r3, [r7, #4]
 8000e16:	6a1b      	ldr	r3, [r3, #32]
 8000e18:	429a      	cmp	r2, r3
 8000e1a:	d106      	bne.n	8000e2a <HAL_RCC_OscConfig+0x4f2>
            (READ_BIT(pll_config, RCC_CFGR_PLLMULL) != RCC_OscInitStruct->PLL.PLLMUL))
 8000e1c:	68fb      	ldr	r3, [r7, #12]
 8000e1e:	f403 1270 	and.w	r2, r3, #3932160	@ 0x3c0000
 8000e22:	687b      	ldr	r3, [r7, #4]
 8000e24:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
        if ((READ_BIT(pll_config, RCC_CFGR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 8000e26:	429a      	cmp	r2, r3
 8000e28:	d001      	beq.n	8000e2e <HAL_RCC_OscConfig+0x4f6>
        {
          return HAL_ERROR;
 8000e2a:	2301      	movs	r3, #1
 8000e2c:	e000      	b.n	8000e30 <HAL_RCC_OscConfig+0x4f8>
        }
      }
    }
  }

  return HAL_OK;
 8000e2e:	2300      	movs	r3, #0
}
 8000e30:	4618      	mov	r0, r3
 8000e32:	3718      	adds	r7, #24
 8000e34:	46bd      	mov	sp, r7
 8000e36:	bd80      	pop	{r7, pc}
 8000e38:	40021000 	.word	0x40021000

08000e3c <HAL_RCC_ClockConfig>:
  *         You can use @ref HAL_RCC_GetClockConfig() function to know which clock is
  *         currently used as system clock source.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_RCC_ClockConfig(RCC_ClkInitTypeDef  *RCC_ClkInitStruct, uint32_t FLatency)
{
 8000e3c:	b580      	push	{r7, lr}
 8000e3e:	b084      	sub	sp, #16
 8000e40:	af00      	add	r7, sp, #0
 8000e42:	6078      	str	r0, [r7, #4]
 8000e44:	6039      	str	r1, [r7, #0]
  uint32_t tickstart;

  /* Check Null pointer */
  if (RCC_ClkInitStruct == NULL)
 8000e46:	687b      	ldr	r3, [r7, #4]
 8000e48:	2b00      	cmp	r3, #0
 8000e4a:	d101      	bne.n	8000e50 <HAL_RCC_ClockConfig+0x14>
  {
    return HAL_ERROR;
 8000e4c:	2301      	movs	r3, #1
 8000e4e:	e0d0      	b.n	8000ff2 <HAL_RCC_ClockConfig+0x1b6>
  must be correctly programmed according to the frequency of the CPU clock
    (HCLK) of the device. */

#if defined(FLASH_ACR_LATENCY)
  /* Increasing the number of wait states because of higher CPU frequency */
  if (FLatency > __HAL_FLASH_GET_LATENCY())
 8000e50:	4b6a      	ldr	r3, [pc, #424]	@ (8000ffc <HAL_RCC_ClockConfig+0x1c0>)
 8000e52:	681b      	ldr	r3, [r3, #0]
 8000e54:	f003 0307 	and.w	r3, r3, #7
 8000e58:	683a      	ldr	r2, [r7, #0]
 8000e5a:	429a      	cmp	r2, r3
 8000e5c:	d910      	bls.n	8000e80 <HAL_RCC_ClockConfig+0x44>
  {
    /* Program the new number of wait states to the LATENCY bits in the FLASH_ACR register */
    __HAL_FLASH_SET_LATENCY(FLatency);
 8000e5e:	4b67      	ldr	r3, [pc, #412]	@ (8000ffc <HAL_RCC_ClockConfig+0x1c0>)
 8000e60:	681b      	ldr	r3, [r3, #0]
 8000e62:	f023 0207 	bic.w	r2, r3, #7
 8000e66:	4965      	ldr	r1, [pc, #404]	@ (8000ffc <HAL_RCC_ClockConfig+0x1c0>)
 8000e68:	683b      	ldr	r3, [r7, #0]
 8000e6a:	4313      	orrs	r3, r2
 8000e6c:	600b      	str	r3, [r1, #0]

    /* Check that the new number of wait states is taken into account to access the Flash
    memory by reading the FLASH_ACR register */
    if (__HAL_FLASH_GET_LATENCY() != FLatency)
 8000e6e:	4b63      	ldr	r3, [pc, #396]	@ (8000ffc <HAL_RCC_ClockConfig+0x1c0>)
 8000e70:	681b      	ldr	r3, [r3, #0]
 8000e72:	f003 0307 	and.w	r3, r3, #7
 8000e76:	683a      	ldr	r2, [r7, #0]
 8000e78:	429a      	cmp	r2, r3
 8000e7a:	d001      	beq.n	8000e80 <HAL_RCC_ClockConfig+0x44>
  {
    return HAL_ERROR;
 8000e7c:	2301      	movs	r3, #1
 8000e7e:	e0b8      	b.n	8000ff2 <HAL_RCC_ClockConfig+0x1b6>
  }
}

#endif /* FLASH_ACR_LATENCY */
/*-------------------------- HCLK Configuration --------------------------*/
if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_HCLK) == RCC_CLOCKTYPE_HCLK)
 8000e80:	687b      	ldr	r3, [r7, #4]
 8000e82:	681b      	ldr	r3, [r3, #0]
 8000e84:	f003 0302 	and.w	r3, r3, #2
 8000e88:	2b00      	cmp	r3, #0
 8000e8a:	d020      	beq.n	8000ece <HAL_RCC_ClockConfig+0x92>
  {
    /* Set the highest APBx dividers in order to ensure that we do not go through
    a non-spec phase whatever we decrease or increase HCLK. */
    if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 8000e8c:	687b      	ldr	r3, [r7, #4]
 8000e8e:	681b      	ldr	r3, [r3, #0]
 8000e90:	f003 0304 	and.w	r3, r3, #4
 8000e94:	2b00      	cmp	r3, #0
 8000e96:	d005      	beq.n	8000ea4 <HAL_RCC_ClockConfig+0x68>
    {
      MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE1, RCC_HCLK_DIV16);
 8000e98:	4b59      	ldr	r3, [pc, #356]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000e9a:	685b      	ldr	r3, [r3, #4]
 8000e9c:	4a58      	ldr	r2, [pc, #352]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000e9e:	f443 63e0 	orr.w	r3, r3, #1792	@ 0x700
 8000ea2:	6053      	str	r3, [r2, #4]
    }

    if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 8000ea4:	687b      	ldr	r3, [r7, #4]
 8000ea6:	681b      	ldr	r3, [r3, #0]
 8000ea8:	f003 0308 	and.w	r3, r3, #8
 8000eac:	2b00      	cmp	r3, #0
 8000eae:	d005      	beq.n	8000ebc <HAL_RCC_ClockConfig+0x80>
    {
      MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE2, (RCC_HCLK_DIV16 << 3));
 8000eb0:	4b53      	ldr	r3, [pc, #332]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000eb2:	685b      	ldr	r3, [r3, #4]
 8000eb4:	4a52      	ldr	r2, [pc, #328]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000eb6:	f443 5360 	orr.w	r3, r3, #14336	@ 0x3800
 8000eba:	6053      	str	r3, [r2, #4]
    }

    /* Set the new HCLK clock divider */
    assert_param(IS_RCC_HCLK(RCC_ClkInitStruct->AHBCLKDivider));
    MODIFY_REG(RCC->CFGR, RCC_CFGR_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
 8000ebc:	4b50      	ldr	r3, [pc, #320]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000ebe:	685b      	ldr	r3, [r3, #4]
 8000ec0:	f023 02f0 	bic.w	r2, r3, #240	@ 0xf0
 8000ec4:	687b      	ldr	r3, [r7, #4]
 8000ec6:	689b      	ldr	r3, [r3, #8]
 8000ec8:	494d      	ldr	r1, [pc, #308]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000eca:	4313      	orrs	r3, r2
 8000ecc:	604b      	str	r3, [r1, #4]
  }

  /*------------------------- SYSCLK Configuration ---------------------------*/
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_SYSCLK) == RCC_CLOCKTYPE_SYSCLK)
 8000ece:	687b      	ldr	r3, [r7, #4]
 8000ed0:	681b      	ldr	r3, [r3, #0]
 8000ed2:	f003 0301 	and.w	r3, r3, #1
 8000ed6:	2b00      	cmp	r3, #0
 8000ed8:	d040      	beq.n	8000f5c <HAL_RCC_ClockConfig+0x120>
  {
    assert_param(IS_RCC_SYSCLKSOURCE(RCC_ClkInitStruct->SYSCLKSource));

    /* HSE is selected as System Clock Source */
    if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_HSE)
 8000eda:	687b      	ldr	r3, [r7, #4]
 8000edc:	685b      	ldr	r3, [r3, #4]
 8000ede:	2b01      	cmp	r3, #1
 8000ee0:	d107      	bne.n	8000ef2 <HAL_RCC_ClockConfig+0xb6>
    {
      /* Check the HSE ready flag */
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 8000ee2:	4b47      	ldr	r3, [pc, #284]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000ee4:	681b      	ldr	r3, [r3, #0]
 8000ee6:	f403 3300 	and.w	r3, r3, #131072	@ 0x20000
 8000eea:	2b00      	cmp	r3, #0
 8000eec:	d115      	bne.n	8000f1a <HAL_RCC_ClockConfig+0xde>
      {
        return HAL_ERROR;
 8000eee:	2301      	movs	r3, #1
 8000ef0:	e07f      	b.n	8000ff2 <HAL_RCC_ClockConfig+0x1b6>
      }
    }
    /* PLL is selected as System Clock Source */
    else if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_PLLCLK)
 8000ef2:	687b      	ldr	r3, [r7, #4]
 8000ef4:	685b      	ldr	r3, [r3, #4]
 8000ef6:	2b02      	cmp	r3, #2
 8000ef8:	d107      	bne.n	8000f0a <HAL_RCC_ClockConfig+0xce>
    {
      /* Check the PLL ready flag */
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == RESET)
 8000efa:	4b41      	ldr	r3, [pc, #260]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000efc:	681b      	ldr	r3, [r3, #0]
 8000efe:	f003 7300 	and.w	r3, r3, #33554432	@ 0x2000000
 8000f02:	2b00      	cmp	r3, #0
 8000f04:	d109      	bne.n	8000f1a <HAL_RCC_ClockConfig+0xde>
      {
        return HAL_ERROR;
 8000f06:	2301      	movs	r3, #1
 8000f08:	e073      	b.n	8000ff2 <HAL_RCC_ClockConfig+0x1b6>
    }
    /* HSI is selected as System Clock Source */
    else
    {
      /* Check the HSI ready flag */
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 8000f0a:	4b3d      	ldr	r3, [pc, #244]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000f0c:	681b      	ldr	r3, [r3, #0]
 8000f0e:	f003 0302 	and.w	r3, r3, #2
 8000f12:	2b00      	cmp	r3, #0
 8000f14:	d101      	bne.n	8000f1a <HAL_RCC_ClockConfig+0xde>
      {
        return HAL_ERROR;
 8000f16:	2301      	movs	r3, #1
 8000f18:	e06b      	b.n	8000ff2 <HAL_RCC_ClockConfig+0x1b6>
      }
    }
    __HAL_RCC_SYSCLK_CONFIG(RCC_ClkInitStruct->SYSCLKSource);
 8000f1a:	4b39      	ldr	r3, [pc, #228]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000f1c:	685b      	ldr	r3, [r3, #4]
 8000f1e:	f023 0203 	bic.w	r2, r3, #3
 8000f22:	687b      	ldr	r3, [r7, #4]
 8000f24:	685b      	ldr	r3, [r3, #4]
 8000f26:	4936      	ldr	r1, [pc, #216]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000f28:	4313      	orrs	r3, r2
 8000f2a:	604b      	str	r3, [r1, #4]

    /* Get Start Tick */
    tickstart = HAL_GetTick();
 8000f2c:	f7ff fa7a 	bl	8000424 <HAL_GetTick>
 8000f30:	60f8      	str	r0, [r7, #12]

    while (__HAL_RCC_GET_SYSCLK_SOURCE() != (RCC_ClkInitStruct->SYSCLKSource << RCC_CFGR_SWS_Pos))
 8000f32:	e00a      	b.n	8000f4a <HAL_RCC_ClockConfig+0x10e>
    {
      if ((HAL_GetTick() - tickstart) > CLOCKSWITCH_TIMEOUT_VALUE)
 8000f34:	f7ff fa76 	bl	8000424 <HAL_GetTick>
 8000f38:	4602      	mov	r2, r0
 8000f3a:	68fb      	ldr	r3, [r7, #12]
 8000f3c:	1ad3      	subs	r3, r2, r3
 8000f3e:	f241 3288 	movw	r2, #5000	@ 0x1388
 8000f42:	4293      	cmp	r3, r2
 8000f44:	d901      	bls.n	8000f4a <HAL_RCC_ClockConfig+0x10e>
      {
        return HAL_TIMEOUT;
 8000f46:	2303      	movs	r3, #3
 8000f48:	e053      	b.n	8000ff2 <HAL_RCC_ClockConfig+0x1b6>
    while (__HAL_RCC_GET_SYSCLK_SOURCE() != (RCC_ClkInitStruct->SYSCLKSource << RCC_CFGR_SWS_Pos))
 8000f4a:	4b2d      	ldr	r3, [pc, #180]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000f4c:	685b      	ldr	r3, [r3, #4]
 8000f4e:	f003 020c 	and.w	r2, r3, #12
 8000f52:	687b      	ldr	r3, [r7, #4]
 8000f54:	685b      	ldr	r3, [r3, #4]
 8000f56:	009b      	lsls	r3, r3, #2
 8000f58:	429a      	cmp	r2, r3
 8000f5a:	d1eb      	bne.n	8000f34 <HAL_RCC_ClockConfig+0xf8>
    }
  }

#if defined(FLASH_ACR_LATENCY)
  /* Decreasing the number of wait states because of lower CPU frequency */
  if (FLatency < __HAL_FLASH_GET_LATENCY())
 8000f5c:	4b27      	ldr	r3, [pc, #156]	@ (8000ffc <HAL_RCC_ClockConfig+0x1c0>)
 8000f5e:	681b      	ldr	r3, [r3, #0]
 8000f60:	f003 0307 	and.w	r3, r3, #7
 8000f64:	683a      	ldr	r2, [r7, #0]
 8000f66:	429a      	cmp	r2, r3
 8000f68:	d210      	bcs.n	8000f8c <HAL_RCC_ClockConfig+0x150>
  {
    /* Program the new number of wait states to the LATENCY bits in the FLASH_ACR register */
    __HAL_FLASH_SET_LATENCY(FLatency);
 8000f6a:	4b24      	ldr	r3, [pc, #144]	@ (8000ffc <HAL_RCC_ClockConfig+0x1c0>)
 8000f6c:	681b      	ldr	r3, [r3, #0]
 8000f6e:	f023 0207 	bic.w	r2, r3, #7
 8000f72:	4922      	ldr	r1, [pc, #136]	@ (8000ffc <HAL_RCC_ClockConfig+0x1c0>)
 8000f74:	683b      	ldr	r3, [r7, #0]
 8000f76:	4313      	orrs	r3, r2
 8000f78:	600b      	str	r3, [r1, #0]

    /* Check that the new number of wait states is taken into account to access the Flash
    memory by reading the FLASH_ACR register */
    if (__HAL_FLASH_GET_LATENCY() != FLatency)
 8000f7a:	4b20      	ldr	r3, [pc, #128]	@ (8000ffc <HAL_RCC_ClockConfig+0x1c0>)
 8000f7c:	681b      	ldr	r3, [r3, #0]
 8000f7e:	f003 0307 	and.w	r3, r3, #7
 8000f82:	683a      	ldr	r2, [r7, #0]
 8000f84:	429a      	cmp	r2, r3
 8000f86:	d001      	beq.n	8000f8c <HAL_RCC_ClockConfig+0x150>
  {
    return HAL_ERROR;
 8000f88:	2301      	movs	r3, #1
 8000f8a:	e032      	b.n	8000ff2 <HAL_RCC_ClockConfig+0x1b6>
  }
}
#endif /* FLASH_ACR_LATENCY */

/*-------------------------- PCLK1 Configuration ---------------------------*/
if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 8000f8c:	687b      	ldr	r3, [r7, #4]
 8000f8e:	681b      	ldr	r3, [r3, #0]
 8000f90:	f003 0304 	and.w	r3, r3, #4
 8000f94:	2b00      	cmp	r3, #0
 8000f96:	d008      	beq.n	8000faa <HAL_RCC_ClockConfig+0x16e>
  {
    assert_param(IS_RCC_PCLK(RCC_ClkInitStruct->APB1CLKDivider));
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE1, RCC_ClkInitStruct->APB1CLKDivider);
 8000f98:	4b19      	ldr	r3, [pc, #100]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000f9a:	685b      	ldr	r3, [r3, #4]
 8000f9c:	f423 62e0 	bic.w	r2, r3, #1792	@ 0x700
 8000fa0:	687b      	ldr	r3, [r7, #4]
 8000fa2:	68db      	ldr	r3, [r3, #12]
 8000fa4:	4916      	ldr	r1, [pc, #88]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000fa6:	4313      	orrs	r3, r2
 8000fa8:	604b      	str	r3, [r1, #4]
  }

  /*-------------------------- PCLK2 Configuration ---------------------------*/
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 8000faa:	687b      	ldr	r3, [r7, #4]
 8000fac:	681b      	ldr	r3, [r3, #0]
 8000fae:	f003 0308 	and.w	r3, r3, #8
 8000fb2:	2b00      	cmp	r3, #0
 8000fb4:	d009      	beq.n	8000fca <HAL_RCC_ClockConfig+0x18e>
  {
    assert_param(IS_RCC_PCLK(RCC_ClkInitStruct->APB2CLKDivider));
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE2, ((RCC_ClkInitStruct->APB2CLKDivider) << 3));
 8000fb6:	4b12      	ldr	r3, [pc, #72]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000fb8:	685b      	ldr	r3, [r3, #4]
 8000fba:	f423 5260 	bic.w	r2, r3, #14336	@ 0x3800
 8000fbe:	687b      	ldr	r3, [r7, #4]
 8000fc0:	691b      	ldr	r3, [r3, #16]
 8000fc2:	00db      	lsls	r3, r3, #3
 8000fc4:	490e      	ldr	r1, [pc, #56]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000fc6:	4313      	orrs	r3, r2
 8000fc8:	604b      	str	r3, [r1, #4]
  }

  /* Update the SystemCoreClock global variable */
  SystemCoreClock = HAL_RCC_GetSysClockFreq() >> AHBPrescTable[(RCC->CFGR & RCC_CFGR_HPRE) >> RCC_CFGR_HPRE_Pos];
 8000fca:	f000 f821 	bl	8001010 <HAL_RCC_GetSysClockFreq>
 8000fce:	4602      	mov	r2, r0
 8000fd0:	4b0b      	ldr	r3, [pc, #44]	@ (8001000 <HAL_RCC_ClockConfig+0x1c4>)
 8000fd2:	685b      	ldr	r3, [r3, #4]
 8000fd4:	091b      	lsrs	r3, r3, #4
 8000fd6:	f003 030f 	and.w	r3, r3, #15
 8000fda:	490a      	ldr	r1, [pc, #40]	@ (8001004 <HAL_RCC_ClockConfig+0x1c8>)
 8000fdc:	5ccb      	ldrb	r3, [r1, r3]
 8000fde:	fa22 f303 	lsr.w	r3, r2, r3
 8000fe2:	4a09      	ldr	r2, [pc, #36]	@ (8001008 <HAL_RCC_ClockConfig+0x1cc>)
 8000fe4:	6013      	str	r3, [r2, #0]

  /* Configure the source of time base considering new system clocks settings*/
  HAL_InitTick(uwTickPrio);
 8000fe6:	4b09      	ldr	r3, [pc, #36]	@ (800100c <HAL_RCC_ClockConfig+0x1d0>)
 8000fe8:	681b      	ldr	r3, [r3, #0]
 8000fea:	4618      	mov	r0, r3
 8000fec:	f7ff f9d8 	bl	80003a0 <HAL_InitTick>

  return HAL_OK;
 8000ff0:	2300      	movs	r3, #0
}
 8000ff2:	4618      	mov	r0, r3
 8000ff4:	3710      	adds	r7, #16
 8000ff6:	46bd      	mov	sp, r7
 8000ff8:	bd80      	pop	{r7, pc}
 8000ffa:	bf00      	nop
 8000ffc:	40022000 	.word	0x40022000
 8001000:	40021000 	.word	0x40021000
 8001004:	08001164 	.word	0x08001164
 8001008:	20000000 	.word	0x20000000
 800100c:	20000004 	.word	0x20000004

08001010 <HAL_RCC_GetSysClockFreq>:
  *         right SYSCLK value. Otherwise, any configuration based on this function will be incorrect.
  *
  * @retval SYSCLK frequency
  */
uint32_t HAL_RCC_GetSysClockFreq(void)
{
 8001010:	b480      	push	{r7}
 8001012:	b087      	sub	sp, #28
 8001014:	af00      	add	r7, sp, #0
#else
  static const uint8_t aPredivFactorTable[2U] = {1, 2};
#endif /*RCC_CFGR2_PREDIV1*/

#endif
  uint32_t tmpreg = 0U, prediv = 0U, pllclk = 0U, pllmul = 0U;
 8001016:	2300      	movs	r3, #0
 8001018:	60fb      	str	r3, [r7, #12]
 800101a:	2300      	movs	r3, #0
 800101c:	60bb      	str	r3, [r7, #8]
 800101e:	2300      	movs	r3, #0
 8001020:	617b      	str	r3, [r7, #20]
 8001022:	2300      	movs	r3, #0
 8001024:	607b      	str	r3, [r7, #4]
  uint32_t sysclockfreq = 0U;
 8001026:	2300      	movs	r3, #0
 8001028:	613b      	str	r3, [r7, #16]
#if defined(RCC_CFGR2_PREDIV1SRC)
  uint32_t prediv2 = 0U, pll2mul = 0U;
#endif /*RCC_CFGR2_PREDIV1SRC*/

  tmpreg = RCC->CFGR;
 800102a:	4b1e      	ldr	r3, [pc, #120]	@ (80010a4 <HAL_RCC_GetSysClockFreq+0x94>)
 800102c:	685b      	ldr	r3, [r3, #4]
 800102e:	60fb      	str	r3, [r7, #12]

  /* Get SYSCLK source -------------------------------------------------------*/
  switch (tmpreg & RCC_CFGR_SWS)
 8001030:	68fb      	ldr	r3, [r7, #12]
 8001032:	f003 030c 	and.w	r3, r3, #12
 8001036:	2b04      	cmp	r3, #4
 8001038:	d002      	beq.n	8001040 <HAL_RCC_GetSysClockFreq+0x30>
 800103a:	2b08      	cmp	r3, #8
 800103c:	d003      	beq.n	8001046 <HAL_RCC_GetSysClockFreq+0x36>
 800103e:	e027      	b.n	8001090 <HAL_RCC_GetSysClockFreq+0x80>
  {
    case RCC_SYSCLKSOURCE_STATUS_HSE:  /* HSE used as system clock */
    {
      sysclockfreq = HSE_VALUE;
 8001040:	4b19      	ldr	r3, [pc, #100]	@ (80010a8 <HAL_RCC_GetSysClockFreq+0x98>)
 8001042:	613b      	str	r3, [r7, #16]
      break;
 8001044:	e027      	b.n	8001096 <HAL_RCC_GetSysClockFreq+0x86>
    }
    case RCC_SYSCLKSOURCE_STATUS_PLLCLK:  /* PLL used as system clock */
    {
      pllmul = aPLLMULFactorTable[(uint32_t)(tmpreg & RCC_CFGR_PLLMULL) >> RCC_CFGR_PLLMULL_Pos];
 8001046:	68fb      	ldr	r3, [r7, #12]
 8001048:	0c9b      	lsrs	r3, r3, #18
 800104a:	f003 030f 	and.w	r3, r3, #15
 800104e:	4a17      	ldr	r2, [pc, #92]	@ (80010ac <HAL_RCC_GetSysClockFreq+0x9c>)
 8001050:	5cd3      	ldrb	r3, [r2, r3]
 8001052:	607b      	str	r3, [r7, #4]
      if ((tmpreg & RCC_CFGR_PLLSRC) != RCC_PLLSOURCE_HSI_DIV2)
 8001054:	68fb      	ldr	r3, [r7, #12]
 8001056:	f403 3380 	and.w	r3, r3, #65536	@ 0x10000
 800105a:	2b00      	cmp	r3, #0
 800105c:	d010      	beq.n	8001080 <HAL_RCC_GetSysClockFreq+0x70>
      {
#if defined(RCC_CFGR2_PREDIV1)
        prediv = aPredivFactorTable[(uint32_t)(RCC->CFGR2 & RCC_CFGR2_PREDIV1) >> RCC_CFGR2_PREDIV1_Pos];
#else
        prediv = aPredivFactorTable[(uint32_t)(RCC->CFGR & RCC_CFGR_PLLXTPRE) >> RCC_CFGR_PLLXTPRE_Pos];
 800105e:	4b11      	ldr	r3, [pc, #68]	@ (80010a4 <HAL_RCC_GetSysClockFreq+0x94>)
 8001060:	685b      	ldr	r3, [r3, #4]
 8001062:	0c5b      	lsrs	r3, r3, #17
 8001064:	f003 0301 	and.w	r3, r3, #1
 8001068:	4a11      	ldr	r2, [pc, #68]	@ (80010b0 <HAL_RCC_GetSysClockFreq+0xa0>)
 800106a:	5cd3      	ldrb	r3, [r2, r3]
 800106c:	60bb      	str	r3, [r7, #8]
        {
          pllclk = pllclk / 2;
        }
#else
        /* HSE used as PLL clock source : PLLCLK = HSE/PREDIV1 * PLLMUL */
        pllclk = (uint32_t)((HSE_VALUE  * pllmul) / prediv);
 800106e:	687b      	ldr	r3, [r7, #4]
 8001070:	4a0d      	ldr	r2, [pc, #52]	@ (80010a8 <HAL_RCC_GetSysClockFreq+0x98>)
 8001072:	fb03 f202 	mul.w	r2, r3, r2
 8001076:	68bb      	ldr	r3, [r7, #8]
 8001078:	fbb2 f3f3 	udiv	r3, r2, r3
 800107c:	617b      	str	r3, [r7, #20]
 800107e:	e004      	b.n	800108a <HAL_RCC_GetSysClockFreq+0x7a>
#endif /*RCC_CFGR2_PREDIV1SRC*/
      }
      else
      {
        /* HSI used as PLL clock source : PLLCLK = HSI/2 * PLLMUL */
        pllclk = (uint32_t)((HSI_VALUE >> 1) * pllmul);
 8001080:	687b      	ldr	r3, [r7, #4]
 8001082:	4a0c      	ldr	r2, [pc, #48]	@ (80010b4 <HAL_RCC_GetSysClockFreq+0xa4>)
 8001084:	fb02 f303 	mul.w	r3, r2, r3
 8001088:	617b      	str	r3, [r7, #20]
      }
      sysclockfreq = pllclk;
 800108a:	697b      	ldr	r3, [r7, #20]
 800108c:	613b      	str	r3, [r7, #16]
      break;
 800108e:	e002      	b.n	8001096 <HAL_RCC_GetSysClockFreq+0x86>
    }
    case RCC_SYSCLKSOURCE_STATUS_HSI:  /* HSI used as system clock source */
    default: /* HSI used as system clock */
    {
      sysclockfreq = HSI_VALUE;
 8001090:	4b05      	ldr	r3, [pc, #20]	@ (80010a8 <HAL_RCC_GetSysClockFreq+0x98>)
 8001092:	613b      	str	r3, [r7, #16]
      break;
 8001094:	bf00      	nop
    }
  }
  return sysclockfreq;
 8001096:	693b      	ldr	r3, [r7, #16]
}
 8001098:	4618      	mov	r0, r3
 800109a:	371c      	adds	r7, #28
 800109c:	46bd      	mov	sp, r7
 800109e:	bc80      	pop	{r7}
 80010a0:	4770      	bx	lr
 80010a2:	bf00      	nop
 80010a4:	40021000 	.word	0x40021000
 80010a8:	007a1200 	.word	0x007a1200
 80010ac:	08001174 	.word	0x08001174
 80010b0:	08001184 	.word	0x08001184
 80010b4:	003d0900 	.word	0x003d0900

080010b8 <RCC_Delay>:
  * @brief  This function provides delay (in milliseconds) based on CPU cycles method.
  * @param  mdelay: specifies the delay time length, in milliseconds.
  * @retval None
  */
static void RCC_Delay(uint32_t mdelay)
{
 80010b8:	b480      	push	{r7}
 80010ba:	b085      	sub	sp, #20
 80010bc:	af00      	add	r7, sp, #0
 80010be:	6078      	str	r0, [r7, #4]
  __IO uint32_t Delay = mdelay * (SystemCoreClock / 8U / 1000U);
 80010c0:	4b0a      	ldr	r3, [pc, #40]	@ (80010ec <RCC_Delay+0x34>)
 80010c2:	681b      	ldr	r3, [r3, #0]
 80010c4:	4a0a      	ldr	r2, [pc, #40]	@ (80010f0 <RCC_Delay+0x38>)
 80010c6:	fba2 2303 	umull	r2, r3, r2, r3
 80010ca:	0a5b      	lsrs	r3, r3, #9
 80010cc:	687a      	ldr	r2, [r7, #4]
 80010ce:	fb02 f303 	mul.w	r3, r2, r3
 80010d2:	60fb      	str	r3, [r7, #12]
  do
  {
    __NOP();
 80010d4:	bf00      	nop
  }
  while (Delay --);
 80010d6:	68fb      	ldr	r3, [r7, #12]
 80010d8:	1e5a      	subs	r2, r3, #1
 80010da:	60fa      	str	r2, [r7, #12]
 80010dc:	2b00      	cmp	r3, #0
 80010de:	d1f9      	bne.n	80010d4 <RCC_Delay+0x1c>
}
 80010e0:	bf00      	nop
 80010e2:	bf00      	nop
 80010e4:	3714      	adds	r7, #20
 80010e6:	46bd      	mov	sp, r7
 80010e8:	bc80      	pop	{r7}
 80010ea:	4770      	bx	lr
 80010ec:	20000000 	.word	0x20000000
 80010f0:	10624dd3 	.word	0x10624dd3

080010f4 <memset>:
 80010f4:	4603      	mov	r3, r0
 80010f6:	4402      	add	r2, r0
 80010f8:	4293      	cmp	r3, r2
 80010fa:	d100      	bne.n	80010fe <memset+0xa>
 80010fc:	4770      	bx	lr
 80010fe:	f803 1b01 	strb.w	r1, [r3], #1
 8001102:	e7f9      	b.n	80010f8 <memset+0x4>

08001104 <__libc_init_array>:
 8001104:	b570      	push	{r4, r5, r6, lr}
 8001106:	2600      	movs	r6, #0
 8001108:	4d0c      	ldr	r5, [pc, #48]	@ (800113c <__libc_init_array+0x38>)
 800110a:	4c0d      	ldr	r4, [pc, #52]	@ (8001140 <__libc_init_array+0x3c>)
 800110c:	1b64      	subs	r4, r4, r5
 800110e:	10a4      	asrs	r4, r4, #2
 8001110:	42a6      	cmp	r6, r4
 8001112:	d109      	bne.n	8001128 <__libc_init_array+0x24>
 8001114:	f000 f81a 	bl	800114c <_init>
 8001118:	2600      	movs	r6, #0
 800111a:	4d0a      	ldr	r5, [pc, #40]	@ (8001144 <__libc_init_array+0x40>)
 800111c:	4c0a      	ldr	r4, [pc, #40]	@ (8001148 <__libc_init_array+0x44>)
 800111e:	1b64      	subs	r4, r4, r5
 8001120:	10a4      	asrs	r4, r4, #2
 8001122:	42a6      	cmp	r6, r4
 8001124:	d105      	bne.n	8001132 <__libc_init_array+0x2e>
 8001126:	bd70      	pop	{r4, r5, r6, pc}
 8001128:	f855 3b04 	ldr.w	r3, [r5], #4
 800112c:	4798      	blx	r3
 800112e:	3601      	adds	r6, #1
 8001130:	e7ee      	b.n	8001110 <__libc_init_array+0xc>
 8001132:	f855 3b04 	ldr.w	r3, [r5], #4
 8001136:	4798      	blx	r3
 8001138:	3601      	adds	r6, #1
 800113a:	e7f2      	b.n	8001122 <__libc_init_array+0x1e>
 800113c:	08001188 	.word	0x08001188
 8001140:	08001188 	.word	0x08001188
 8001144:	08001188 	.word	0x08001188
 8001148:	0800118c 	.word	0x0800118c

0800114c <_init>:
 800114c:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 800114e:	bf00      	nop
 8001150:	bcf8      	pop	{r3, r4, r5, r6, r7}
 8001152:	bc08      	pop	{r3}
 8001154:	469e      	mov	lr, r3
 8001156:	4770      	bx	lr

08001158 <_fini>:
 8001158:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 800115a:	bf00      	nop
 800115c:	bcf8      	pop	{r3, r4, r5, r6, r7}
 800115e:	bc08      	pop	{r3}
 8001160:	469e      	mov	lr, r3
 8001162:	4770      	bx	lr
