../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:116:13:P<PERSON>_OverloadWfe	4	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:155:6:HAL_PWR_DeInit	4	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:168:6:HAL_PWR_EnableBkUpAccess	4	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:181:6:HAL_PWR_DisableBkUpAccess	4	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:315:6:HAL_PWR_ConfigPVD	16	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:358:6:H<PERSON>_PWR_EnablePVD	4	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:368:6:HAL_PWR_DisablePVD	4	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:381:6:HAL_PWR_EnableWakeUpPin	24	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:396:6:HAL_PWR_DisableWakeUpPin	24	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:416:6:HAL_PWR_EnterSLEEPMode	16	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:462:6:HAL_PWR_EnterSTOPMode	16	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:502:6:HAL_PWR_EnterSTANDBYMode	4	static,ignoring_inline_asm
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:527:6:HAL_PWR_EnableSleepOnExit	4	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:540:6:HAL_PWR_DisableSleepOnExit	4	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:553:6:HAL_PWR_EnableSEVOnPend	4	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:566:6:HAL_PWR_DisableSEVOnPend	4	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:579:6:HAL_PWR_PVD_IRQHandler	8	static
../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c:596:13:HAL_PWR_PVDCallback	4	static
