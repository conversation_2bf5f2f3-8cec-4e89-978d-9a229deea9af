../Core/Src/syscalls.c:44:6:initialise_monitor_handles	1
../Core/Src/syscalls.c:48:5:_getpid	1
../Core/Src/syscalls.c:53:5:_kill	1
../Core/Src/syscalls.c:61:6:_exit	1
../Core/Src/syscalls.c:67:27:_read	2
../Core/Src/syscalls.c:80:27:_write	2
../Core/Src/syscalls.c:92:5:_close	1
../Core/Src/syscalls.c:99:5:_fstat	1
../Core/Src/syscalls.c:106:5:_isatty	1
../Core/Src/syscalls.c:112:5:_lseek	1
../Core/Src/syscalls.c:120:5:_open	1
../Core/Src/syscalls.c:128:5:_wait	1
../Core/Src/syscalls.c:135:5:_unlink	1
../Core/Src/syscalls.c:142:5:_times	1
../Core/Src/syscalls.c:148:5:_stat	1
../Core/Src/syscalls.c:155:5:_link	1
../Core/Src/syscalls.c:163:5:_fork	1
../Core/Src/syscalls.c:169:5:_execve	1
